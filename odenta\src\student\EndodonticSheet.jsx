import { useState } from 'react';
import { motion } from 'framer-motion';
import MedicalTab from './MedicalTab';
import { generateSheetPDF } from '../utils/pdfUtils';

const EndodonticSheet = ({ initialData, onSave }) => {
  const defaultFormData = {
    subjective: {
      historyOfPresentIllness: {
        natureOfPain: '',
        onset: '',
        location: '',
        duration: '',
        initiatedBy: '',
        relievedBy: ''
      }
    },
    objective: {
      extraoral: {
        facial: '',
        swelling: '',
        lymphNodesSwollen: ''
      },
      intraoral: {
        swelling: '',
        sinusTract: '',
        clinicalCrown: {
          restoration: '',
          caries: '',
          fracture: '',
          exposure: '',
          discoloration: ''
        }
      },
      periodontalExamination: [
        {
          toothNumber: '',
          cold: '',
          heat: '',
          percussion: '',
          palpation: '',
          mobility: '',
          biteStick: '',
          mb: '',
          b: '',
          db: '',
          dl: '',
          l: '',
          ml: '',
          bleed: '',
          recession: '',
          furcation: ''
        }
      ]
    },
    radiographicExamination: {
      crown: '',
      pulpChamber: '',
      roots: '',
      rootCanal: '',
      laminaDura: '',
      alveolarBone: '',
      sinusTracts: ''
    },
    assessment: {
      pulpal: '',
      periapical: '',
      etiology: '',
      prognosis: ''
    },
    treatmentPlan: {
      endodontic: '',
      periodontal: '',
      restorative: ''
    }
  };

  const mergedData = initialData && Object.keys(initialData).length > 0
    ? { ...defaultFormData, ...initialData }
    : defaultFormData;

  const [formData, setFormData] = useState(mergedData);
  const [errors, setErrors] = useState({});
  const [activeTab, setActiveTab] = useState('medical');

  // Modified validation to make fields optional
  const validateForm = () => {
    // Since all fields are now optional, we're just returning true
    // You can add custom validation logic here if needed for specific fields
    return true;
  };

  const handleChange = (section, subsection, field, value) => {
    setFormData((prev) => ({
      ...prev,
      [section]: {
        ...prev[section],
        [subsection]: {
          ...prev[section][subsection],
          [field]: value
        }
      }
    }));
    setErrors((prev) => ({ ...prev, [`${section}.${subsection}.${field}`]: '' }));
  };

  const handleNestedChange = (section, parent, subsection, field, value) => {
    setFormData((prev) => ({
      ...prev,
      [section]: {
        ...prev[section],
        [parent]: {
          ...prev[section][parent],
          [subsection]: {
            ...prev[section][parent][subsection],
            [field]: value
          }
        }
      }
    }));
    setErrors((prev) => ({ ...prev, [`${section}.${parent}.${subsection}.${field}`]: '' }));
  };

  const handleDirectChange = (section, field, value) => {
    setFormData((prev) => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
    setErrors((prev) => ({ ...prev, [`${section}.${field}`]: '' }));
  };

  const handlePeriodontalRowChange = (section, subsection, rowIndex, field, value) => {
    setFormData((prev) => {
      const updatedRows = [...prev[section][subsection]];
      updatedRows[rowIndex] = {
        ...updatedRows[rowIndex],
        [field]: value
      };
      return {
        ...prev,
        [section]: {
          ...prev[section],
          [subsection]: updatedRows
        }
      };
    });
    setErrors((prev) => ({ ...prev, [`${section}.${subsection}.${rowIndex}.${field}`]: '' }));
  };

  const addPeriodontalRow = () => {
    setFormData((prev) => ({
      ...prev,
      objective: {
        ...prev.objective,
        periodontalExamination: [
          ...prev.objective.periodontalExamination,
          {
            toothNumber: '',
            cold: '',
            heat: '',
            percussion: '',
            palpation: '',
            mobility: '',
            biteStick: '',
            mb: '',
            b: '',
            db: '',
            dl: '',
            l: '',
            ml: '',
            bleed: '',
            recession: '',
            furcation: ''
          }
        ]
      }
    }));
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      alert('Please fill all required fields.');
      return;
    }

    // Create diagnosis and treatment plan strings
    const diagnosis = `Endodontic diagnosis: ${formData.assessment?.pulpalDiagnosis || 'Not specified'} / ${formData.assessment?.periapicalDiagnosis || 'Not specified'}`;

    const treatmentPlan = `Endodontic treatment plan: ${formData.treatmentPlan?.endodontic || 'Not specified'}`;

    // Call the onSave function from props
    if (onSave) {
      const success = await onSave(formData, diagnosis, treatmentPlan, '');
      if (!success) {
        alert('Failed to save sheet. Please try again.');
      }
    } else {
      alert('Form submitted successfully!');
    }
  };

  const handleDownloadPDF = async () => {
    try {
      // Create diagnosis and treatment plan strings
      const diagnosis = `Endodontic diagnosis: ${formData.assessment?.pulpalDiagnosis || 'Not specified'} / ${formData.assessment?.periapicalDiagnosis || 'Not specified'}`;
      const treatmentPlan = `Endodontic treatment plan: ${formData.treatmentPlan?.endodontic || 'Not specified'}`;

      // Create a mock sheet object for PDF generation
      const mockSheet = {
        type: 'Endodontics',
        createdAt: new Date().toISOString(),
        details: {
          diagnosis,
          treatmentPlan,
          specificData: formData
        }
      };

      await generateSheetPDF(mockSheet, 'Current_Patient');
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Failed to generate PDF. Please try again.');
    }
  };

  const renderRadioButtons = (label, value, onChange, options, error, required = false) => (
    <div className="mb-5">
      <label className="block text-base font-medium text-gray-700 mb-2">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
        {options.map((option) => (
          <div
            key={option}
            className={`flex items-center p-2 rounded-lg border transition-all ${
              value === option
                ? 'bg-[rgba(40,167,69,0.1)] border-[#28A745]'
                : 'bg-white border-gray-200 hover:bg-gray-50'
            }`}
          >
            <input
              type="radio"
              id={`${label.replace(/\s+/g, '')}-${option}`}
              name={label.replace(/\s+/g, '')}
              value={option}
              checked={value === option}
              onChange={onChange}
              className={`h-4 w-4 ${value === option ? 'text-[#28A745] focus:ring-[#28A745]' : 'text-[#0077B6] focus:ring-[#0077B6]'} border-gray-300`}
            />
            <label
              htmlFor={`${label.replace(/\s+/g, '')}-${option}`}
              className={`ml-2 text-sm cursor-pointer w-full ${value === option ? 'font-medium text-[#28A745]' : 'text-gray-700'}`}
            >
              {option}
            </label>
          </div>
        ))}
      </div>
      {error && <p className="text-red-500 text-xs mt-1">{error}</p>}
    </div>
  );

  const renderInput = (label, value, onChange, error, type = 'text', required = false) => (
    <div className="mb-4">
      <label className="block text-base font-medium text-gray-700 mb-1">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      <input
        type={type}
        value={value}
        onChange={onChange}
        className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6] transition-all ${error ? 'border-red-500' : 'border-gray-300'}`}
      />
      {error && <p className="text-red-500 text-xs mt-1">{error}</p>}
    </div>
  );

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="w-full mx-auto mt-8 p-6 bg-white rounded-xl shadow-md border border-[rgba(0,119,182,0.1)]"
    >
      <h3 className="text-2xl font-bold text-[#0077B6] mb-6">Endodontic Examination Sheet</h3>

      {/* Tabs */}
      <div className="flex border-b border-[rgba(0,119,182,0.1)] mb-6 overflow-x-auto">
        <button
          className={`px-4 py-2 font-medium transition-all ${activeTab === 'medical' ? 'border-b-2 border-[#0077B6] text-[#0077B6] bg-[rgba(0,119,182,0.05)]' : 'text-gray-500 hover:text-[#0077B6] hover:bg-[rgba(0,119,182,0.03)]'}`}
          onClick={() => setActiveTab('medical')}
        >
          Medical
        </button>
        <button
          className={`px-4 py-2 font-medium transition-all ${activeTab === 'subjective' ? 'border-b-2 border-[#0077B6] text-[#0077B6] bg-[rgba(0,119,182,0.05)]' : 'text-gray-500 hover:text-[#0077B6] hover:bg-[rgba(0,119,182,0.03)]'}`}
          onClick={() => setActiveTab('subjective')}
        >
          Subjective
        </button>
        <button
          className={`px-4 py-2 font-medium transition-all ${activeTab === 'objective' ? 'border-b-2 border-[#0077B6] text-[#0077B6] bg-[rgba(0,119,182,0.05)]' : 'text-gray-500 hover:text-[#0077B6] hover:bg-[rgba(0,119,182,0.03)]'}`}
          onClick={() => setActiveTab('objective')}
        >
          Objective
        </button>
        <button
          className={`px-4 py-2 font-medium transition-all ${activeTab === 'radiographicExamination' ? 'border-b-2 border-[#0077B6] text-[#0077B6] bg-[rgba(0,119,182,0.05)]' : 'text-gray-500 hover:text-[#0077B6] hover:bg-[rgba(0,119,182,0.03)]'}`}
          onClick={() => setActiveTab('radiographicExamination')}
        >
          Radiographic
        </button>
        <button
          className={`px-4 py-2 font-medium transition-all ${activeTab === 'assessment' ? 'border-b-2 border-[#0077B6] text-[#0077B6] bg-[rgba(0,119,182,0.05)]' : 'text-gray-500 hover:text-[#0077B6] hover:bg-[rgba(0,119,182,0.03)]'}`}
          onClick={() => setActiveTab('assessment')}
        >
          Assessment
        </button>
        <button
          className={`px-4 py-2 font-medium transition-all ${activeTab === 'treatmentPlan' ? 'border-b-2 border-[#0077B6] text-[#0077B6] bg-[rgba(0,119,182,0.05)]' : 'text-gray-500 hover:text-[#0077B6] hover:bg-[rgba(0,119,182,0.03)]'}`}
          onClick={() => setActiveTab('treatmentPlan')}
        >
          Treatment Plan
        </button>
      </div>

      {activeTab === 'medical' && (
        <div>
          <MedicalTab onSave={(medicalInfo) => {
            console.log('Medical info updated:', medicalInfo);
            // You can update the form data here if needed
          }} />
        </div>
      )}

      {activeTab === 'subjective' && (
        <div>
          <div className="mb-6 bg-white p-5 rounded-lg border border-gray-200 shadow-sm">
            <h4 className="text-lg font-semibold text-[#0077B6] mb-3">History of Present Illness</h4>
            {renderRadioButtons(
              'Nature of Pain',
              formData.subjective.historyOfPresentIllness.natureOfPain,
              (e) => handleChange('subjective', 'historyOfPresentIllness', 'natureOfPain', e.target.value),
              ['None', 'Mild', 'Moderate', 'Severe'],
              errors['subjective.historyOfPresentIllness.natureOfPain']
            )}
            {renderRadioButtons(
              'Onset',
              formData.subjective.historyOfPresentIllness.onset,
              (e) => handleChange('subjective', 'historyOfPresentIllness', 'onset', e.target.value),
              ['Dull', 'Sharp', 'Throbbing', 'Constant'],
              errors['subjective.historyOfPresentIllness.onset']
            )}
            {renderRadioButtons(
              'Location',
              formData.subjective.historyOfPresentIllness.location,
              (e) => handleChange('subjective', 'historyOfPresentIllness', 'location', e.target.value),
              ['Localized', 'Diffuse', 'Referred', 'Radiating to:'],
              errors['subjective.historyOfPresentIllness.location']
            )}
            {renderRadioButtons(
              'Duration',
              formData.subjective.historyOfPresentIllness.duration,
              (e) => handleChange('subjective', 'historyOfPresentIllness', 'duration', e.target.value),
              ['Seconds', 'Minutes', 'Hours', 'Constant'],
              errors['subjective.historyOfPresentIllness.duration']
            )}
            {renderRadioButtons(
              'Initiated by',
              formData.subjective.historyOfPresentIllness.initiatedBy,
              (e) => handleChange('subjective', 'historyOfPresentIllness', 'initiatedBy', e.target.value),
              ['Cold', 'Heat', 'Sweets', 'Spontaneous', 'Palpation', 'Mastication', 'Supination'],
              errors['subjective.historyOfPresentIllness.initiatedBy']
            )}
            {renderRadioButtons(
              'Relieved by',
              formData.subjective.historyOfPresentIllness.relievedBy,
              (e) => handleChange('subjective', 'historyOfPresentIllness', 'relievedBy', e.target.value),
              ['Cold', 'Heat', 'OTC-Meds', 'Narc-Meds'],
              errors['subjective.historyOfPresentIllness.relievedBy']
            )}
          </div>
        </div>
      )}

      {activeTab === 'objective' && (
        <div>
          <div className="mb-6 bg-white p-5 rounded-lg border border-gray-200 shadow-sm">
            <h4 className="text-lg font-semibold text-[#0077B6] mb-3">Extraoral</h4>
            {renderRadioButtons(
              'Facial',
              formData.objective.extraoral.facial,
              (e) => handleChange('objective', 'extraoral', 'facial', e.target.value),
              ['Yes', 'No'],
              errors['objective.extraoral.facial']
            )}
            {renderRadioButtons(
              'Swelling',
              formData.objective.extraoral.swelling,
              (e) => handleChange('objective', 'extraoral', 'swelling', e.target.value),
              ['Yes', 'No'],
              errors['objective.extraoral.swelling']
            )}
            {renderRadioButtons(
              'Lymph Nodes Swollen',
              formData.objective.extraoral.lymphNodesSwollen,
              (e) => handleChange('objective', 'extraoral', 'lymphNodesSwollen', e.target.value),
              ['Yes', 'No'],
              errors['objective.extraoral.lymphNodesSwollen']
            )}
          </div>
          <div className="mb-6 bg-white p-5 rounded-lg border border-gray-200 shadow-sm">
            <h4 className="text-lg font-semibold text-[#0077B6] mb-3">Intraoral</h4>
            {renderRadioButtons(
              'Swelling',
              formData.objective.intraoral.swelling,
              (e) => handleChange('objective', 'intraoral', 'swelling', e.target.value),
              ['Yes', 'No', 'Mild', 'Moderate', 'Severe', 'Location:'],
              errors['objective.intraoral.swelling']
            )}
            {renderRadioButtons(
              'Sinus Tract',
              formData.objective.intraoral.sinusTract,
              (e) => handleChange('objective', 'intraoral', 'sinusTract', e.target.value),
              ['Yes', 'No', 'Closed'],
              errors['objective.intraoral.sinusTract']
            )}
            {renderRadioButtons(
              'Clinical Crown - Restoration',
              formData.objective.intraoral.clinicalCrown.restoration,
              (e) => handleNestedChange('objective', 'intraoral', 'clinicalCrown', 'restoration', e.target.value),
              ['Yes', 'No'],
              errors['objective.intraoral.clinicalCrown.restoration']
            )}
            {renderRadioButtons(
              'Clinical Crown - Caries',
              formData.objective.intraoral.clinicalCrown.caries,
              (e) => handleNestedChange('objective', 'intraoral', 'clinicalCrown', 'caries', e.target.value),
              ['Yes', 'No'],
              errors['objective.intraoral.clinicalCrown.caries']
            )}
            {renderRadioButtons(
              'Clinical Crown - Fracture',
              formData.objective.intraoral.clinicalCrown.fracture,
              (e) => handleNestedChange('objective', 'intraoral', 'clinicalCrown', 'fracture', e.target.value),
              ['Yes', 'No'],
              errors['objective.intraoral.clinicalCrown.fracture']
            )}
            {renderRadioButtons(
              'Clinical Crown - Exposure',
              formData.objective.intraoral.clinicalCrown.exposure,
              (e) => handleNestedChange('objective', 'intraoral', 'clinicalCrown', 'exposure', e.target.value),
              ['Yes', 'No'],
              errors['objective.intraoral.clinicalCrown.exposure']
            )}
            {renderRadioButtons(
              'Clinical Crown - Discoloration',
              formData.objective.intraoral.clinicalCrown.discoloration,
              (e) => handleNestedChange('objective', 'intraoral', 'clinicalCrown', 'discoloration', e.target.value),
              ['Yes', 'No'],
              errors['objective.intraoral.clinicalCrown.discoloration']
            )}
          </div>
          <div className="mb-6 bg-white p-5 rounded-lg border border-gray-200 shadow-sm">
            <h4 className="text-lg font-semibold text-[#0077B6] mb-2">Periodontal Examination</h4>
            <p className="text-sm text-red-600 mb-2">
              (Normal: N &nbsp; No response: 0 &nbsp; Mild: + &nbsp; Moderate: ++ &nbsp; Severe: +++ &nbsp; Lingered: L &nbsp; Delayed: D)
            </p>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse border border-gray-300">
                <thead>
                  <tr className="bg-gray-100">
                    <th className="border border-gray-300 px-2 py-1 text-sm">Tooth #</th>
                    <th className="border border-gray-300 px-2 py-1 text-sm">Cold</th>
                    <th className="border border-gray-300 px-2 py-1 text-sm">Heat</th>
                    <th className="border border-gray-300 px-2 py-1 text-sm">Percussion</th>
                    <th className="border border-gray-300 px-2 py-1 text-sm">Palpation</th>
                    <th className="border border-gray-300 px-2 py-1 text-sm">Mobility</th>
                    <th className="border border-gray-300 px-2 py-1 text-sm">Bite stick</th>
                    <th className="border border-gray-300 px-2 py-1 text-sm">MB</th>
                    <th className="border border-gray-300 px-2 py-1 text-sm">B</th>
                    <th className="border border-gray-300 px-2 py-1 text-sm">DB</th>
                    <th className="border border-gray-300 px-2 py-1 text-sm">DL</th>
                    <th className="border border-gray-300 px-2 py-1 text-sm">L</th>
                    <th className="border border-gray-300 px-2 py-1 text-sm">ML</th>
                    <th className="border border-gray-300 px-2 py-1 text-sm">Bleed</th>
                    <th className="border border-gray-300 px-2 py-1 text-sm">Recession</th>
                    <th className="border border-gray-300 px-2 py-1 text-sm">Furcation</th>
                  </tr>
                </thead>
                <tbody>
                  {formData.objective.periodontalExamination.map((row, rowIndex) => (
                    <tr key={rowIndex}>
                      <td className="border border-gray-300 px-2 py-1">
                        <input
                          type="text"
                          value={row.toothNumber}
                          onChange={(e) => handlePeriodontalRowChange('objective', 'periodontalExamination', rowIndex, 'toothNumber', e.target.value)}
                          className={`w-full px-1 py-1 text-sm border rounded ${errors[`objective.periodontalExamination.${rowIndex}.toothNumber`] ? 'border-red-500' : 'border-gray-300'}`}
                        />
                        {errors[`objective.periodontalExamination.${rowIndex}.toothNumber`] && (
                          <p className="text-red-500 text-xs mt-1">This field is required</p>
                        )}
                      </td>
                      <td className="border border-gray-300 px-2 py-1">
                        <input
                          type="text"
                          value={row.cold}
                          onChange={(e) => handlePeriodontalRowChange('objective', 'periodontalExamination', rowIndex, 'cold', e.target.value)}
                          className={`w-full px-1 py-1 text-sm border rounded ${errors[`objective.periodontalExamination.${rowIndex}.cold`] ? 'border-red-500' : 'border-gray-300'}`}
                        />
                        {errors[`objective.periodontalExamination.${rowIndex}.cold`] && (
                          <p className="text-red-500 text-xs mt-1">This field is required</p>
                        )}
                      </td>
                      <td className="border border-gray-300 px-2 py-1">
                        <input
                          type="text"
                          value={row.heat}
                          onChange={(e) => handlePeriodontalRowChange('objective', 'periodontalExamination', rowIndex, 'heat', e.target.value)}
                          className={`w-full px-1 py-1 text-sm border rounded ${errors[`objective.periodontalExamination.${rowIndex}.heat`] ? 'border-red-500' : 'border-gray-300'}`}
                        />
                        {errors[`objective.periodontalExamination.${rowIndex}.heat`] && (
                          <p className="text-red-500 text-xs mt-1">This field is required</p>
                        )}
                      </td>
                      <td className="border border-gray-300 px-2 py-1">
                        <input
                          type="text"
                          value={row.percussion}
                          onChange={(e) => handlePeriodontalRowChange('objective', 'periodontalExamination', rowIndex, 'percussion', e.target.value)}
                          className={`w-full px-1 py-1 text-sm border rounded ${errors[`objective.periodontalExamination.${rowIndex}.percussion`] ? 'border-red-500' : 'border-gray-300'}`}
                        />
                        {errors[`objective.periodontalExamination.${rowIndex}.percussion`] && (
                          <p className="text-red-500 text-xs mt-1">This field is required</p>
                        )}
                      </td>
                      <td className="border border-gray-300 px-2 py-1">
                        <input
                          type="text"
                          value={row.palpation}
                          onChange={(e) => handlePeriodontalRowChange('objective', 'periodontalExamination', rowIndex, 'palpation', e.target.value)}
                          className={`w-full px-1 py-1 text-sm border rounded ${errors[`objective.periodontalExamination.${rowIndex}.palpation`] ? 'border-red-500' : 'border-gray-300'}`}
                        />
                        {errors[`objective.periodontalExamination.${rowIndex}.palpation`] && (
                          <p className="text-red-500 text-xs mt-1">This field is required</p>
                        )}
                      </td>
                      <td className="border border-gray-300 px-2 py-1">
                        <input
                          type="text"
                          value={row.mobility}
                          onChange={(e) => handlePeriodontalRowChange('objective', 'periodontalExamination', rowIndex, 'mobility', e.target.value)}
                          className={`w-full px-1 py-1 text-sm border rounded ${errors[`objective.periodontalExamination.${rowIndex}.mobility`] ? 'border-red-500' : 'border-gray-300'}`}
                        />
                        {errors[`objective.periodontalExamination.${rowIndex}.mobility`] && (
                          <p className="text-red-500 text-xs mt-1">This field is required</p>
                        )}
                      </td>
                      <td className="border border-gray-300 px-2 py-1">
                        <input
                          type="text"
                          value={row.biteStick}
                          onChange={(e) => handlePeriodontalRowChange('objective', 'periodontalExamination', rowIndex, 'biteStick', e.target.value)}
                          className={`w-full px-1 py-1 text-sm border rounded ${errors[`objective.periodontalExamination.${rowIndex}.biteStick`] ? 'border-red-500' : 'border-gray-300'}`}
                        />
                        {errors[`objective.periodontalExamination.${rowIndex}.biteStick`] && (
                          <p className="text-red-500 text-xs mt-1">This field is required</p>
                        )}
                      </td>
                      <td className="border border-gray-300 px-2 py-1">
                        <input
                          type="text"
                          value={row.mb}
                          onChange={(e) => handlePeriodontalRowChange('objective', 'periodontalExamination', rowIndex, 'mb', e.target.value)}
                          className={`w-full px-1 py-1 text-sm border rounded ${errors[`objective.periodontalExamination.${rowIndex}.mb`] ? 'border-red-500' : 'border-gray-300'}`}
                        />
                        {errors[`objective.periodontalExamination.${rowIndex}.mb`] && (
                          <p className="text-red-500 text-xs mt-1">This field is required</p>
                        )}
                      </td>
                      <td className="border border-gray-300 px-2 py-1">
                        <input
                          type="text"
                          value={row.b}
                          onChange={(e) => handlePeriodontalRowChange('objective', 'periodontalExamination', rowIndex, 'b', e.target.value)}
                          className={`w-full px-1 py-1 text-sm border rounded ${errors[`objective.periodontalExamination.${rowIndex}.b`] ? 'border-red-500' : 'border-gray-300'}`}
                        />
                        {errors[`objective.periodontalExamination.${rowIndex}.b`] && (
                          <p className="text-red-500 text-xs mt-1">This field is required</p>
                        )}
                      </td>
                      <td className="border border-gray-300 px-2 py-1">
                        <input
                          type="text"
                          value={row.db}
                          onChange={(e) => handlePeriodontalRowChange('objective', 'periodontalExamination', rowIndex, 'db', e.target.value)}
                          className={`w-full px-1 py-1 text-sm border rounded ${errors[`objective.periodontalExamination.${rowIndex}.db`] ? 'border-red-500' : 'border-gray-300'}`}
                        />
                        {errors[`objective.periodontalExamination.${rowIndex}.db`] && (
                          <p className="text-red-500 text-xs mt-1">This field is required</p>
                        )}
                      </td>
                      <td className="border border-gray-300 px-2 py-1">
                        <input
                          type="text"
                          value={row.dl}
                          onChange={(e) => handlePeriodontalRowChange('objective', 'periodontalExamination', rowIndex, 'dl', e.target.value)}
                          className={`w-full px-1 py-1 text-sm border rounded ${errors[`objective.periodontalExamination.${rowIndex}.dl`] ? 'border-red-500' : 'border-gray-300'}`}
                        />
                        {errors[`objective.periodontalExamination.${rowIndex}.dl`] && (
                          <p className="text-red-500 text-xs mt-1">This field is required</p>
                        )}
                      </td>
                      <td className="border border-gray-300 px-2 py-1">
                        <input
                          type="text"
                          value={row.l}
                          onChange={(e) => handlePeriodontalRowChange('objective', 'periodontalExamination', rowIndex, 'l', e.target.value)}
                          className={`w-full px-1 py-1 text-sm border rounded ${errors[`objective.periodontalExamination.${rowIndex}.l`] ? 'border-red-500' : 'border-gray-300'}`}
                        />
                        {errors[`objective.periodontalExamination.${rowIndex}.l`] && (
                          <p className="text-red-500 text-xs mt-1">This field is required</p>
                        )}
                      </td>
                      <td className="border border-gray-300 px-2 py-1">
                        <input
                          type="text"
                          value={row.ml}
                          onChange={(e) => handlePeriodontalRowChange('objective', 'periodontalExamination', rowIndex, 'ml', e.target.value)}
                          className={`w-full px-1 py-1 text-sm border rounded ${errors[`objective.periodontalExamination.${rowIndex}.ml`] ? 'border-red-500' : 'border-gray-300'}`}
                        />
                        {errors[`objective.periodontalExamination.${rowIndex}.ml`] && (
                          <p className="text-red-500 text-xs mt-1">This field is required</p>
                        )}
                      </td>
                      <td className="border border-gray-300 px-2 py-1">
                        <input
                          type="text"
                          value={row.bleed}
                          onChange={(e) => handlePeriodontalRowChange('objective', 'periodontalExamination', rowIndex, 'bleed', e.target.value)}
                          className={`w-full px-1 py-1 text-sm border rounded ${errors[`objective.periodontalExamination.${rowIndex}.bleed`] ? 'border-red-500' : 'border-gray-300'}`}
                        />
                        {errors[`objective.periodontalExamination.${rowIndex}.bleed`] && (
                          <p className="text-red-500 text-xs mt-1">This field is required</p>
                        )}
                      </td>
                      <td className="border border-gray-300 px-2 py-1">
                        <input
                          type="text"
                          value={row.recession}
                          onChange={(e) => handlePeriodontalRowChange('objective', 'periodontalExamination', rowIndex, 'recession', e.target.value)}
                          className={`w-full px-1 py-1 text-sm border rounded ${errors[`objective.periodontalExamination.${rowIndex}.recession`] ? 'border-red-500' : 'border-gray-300'}`}
                        />
                        {errors[`objective.periodontalExamination.${rowIndex}.recession`] && (
                          <p className="text-red-500 text-xs mt-1">This field is required</p>
                        )}
                      </td>
                      <td className="border border-gray-300 px-2 py-1">
                        <input
                          type="text"
                          value={row.furcation}
                          onChange={(e) => handlePeriodontalRowChange('objective', 'periodontalExamination', rowIndex, 'furcation', e.target.value)}
                          className={`w-full px-1 py-1 text-sm border rounded ${errors[`objective.periodontalExamination.${rowIndex}.furcation`] ? 'border-red-500' : 'border-gray-300'}`}
                        />
                        {errors[`objective.periodontalExamination.${rowIndex}.furcation`] && (
                          <p className="text-red-500 text-xs mt-1">This field is required</p>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            <button
              onClick={addPeriodontalRow}
              className="mt-4 px-4 py-2 bg-[#28A745] text-white font-medium rounded-lg hover:bg-[#218838] transition-all shadow-sm"
            >
              Add Another Row
            </button>
          </div>
        </div>
      )}

      {activeTab === 'radiographicExamination' && (
        <div>
          <div className="mb-6 bg-white p-5 rounded-lg border border-gray-200 shadow-sm">
            <h4 className="text-lg font-semibold text-[#0077B6] mb-3">Radiographic Examination</h4>
            <p className="text-sm text-red-600 mb-2">
            (WNL: within normal limit &nbsp; RCT: Root canal treatment &nbsp; IAC: Inferior alveolar canal)
            </p>
            {renderRadioButtons(
              'Crown',
              formData.radiographicExamination.crown,
              (e) => handleDirectChange('radiographicExamination', 'crown', e.target.value),
              ['WNL', 'Caries', 'Restoration', 'Crown', 'Dens in dente'],
              errors['radiographicExamination.crown']
            )}
            {renderRadioButtons(
              'Pulp Chamber',
              formData.radiographicExamination.pulpChamber,
              (e) => handleDirectChange('radiographicExamination', 'pulpChamber', e.target.value),
              ['WNL', 'Pulp stone', 'Exposure', 'Resorption', 'Perforation'],
              errors['radiographicExamination.pulpChamber']
            )}
            {renderRadioButtons(
              'Roots',
              formData.radiographicExamination.roots,
              (e) => handleDirectChange('radiographicExamination', 'roots', e.target.value),
              ['WNL', 'Curvature', 'Dilaceration', 'Resorption', 'Perforation', 'Fracture', 'Open apex', 'Sinus/ IAC'],
              errors['radiographicExamination.roots']
            )}
            {renderRadioButtons(
              'Root Canal',
              formData.radiographicExamination.rootCanal,
              (e) => handleDirectChange('radiographicExamination', 'rootCanal', e.target.value),
              ['WNL', 'Calcification', 'Bifurcated', 'Resorption', 'Prior RCT', 'Perforation', 'Furcation'],
              errors['radiographicExamination.rootCanal']
            )}
            {renderRadioButtons(
              'Lamina Dura',
              formData.radiographicExamination.laminaDura,
              (e) => handleDirectChange('radiographicExamination', 'laminaDura', e.target.value),
              ['WNL', 'Obscure', 'Broken', 'Widened'],
              errors['radiographicExamination.laminaDura']
            )}
            {renderRadioButtons(
              'Alveolar Bone',
              formData.radiographicExamination.alveolarBone,
              (e) => handleDirectChange('radiographicExamination', 'alveolarBone', e.target.value),
              ['WNL', 'Apical radiolucency', 'Lateral radiolucency', 'Ap/Lat opacity', 'Crestal bone loss'],
              errors['radiographicExamination.alveolarBone']
            )}
            {renderInput(
              'Sinus Tracts',
              formData.radiographicExamination.sinusTracts,
              (e) => handleDirectChange('radiographicExamination', 'sinusTracts', e.target.value),
              errors['radiographicExamination.sinusTracts']
            )}
          </div>
        </div>
      )}

      {activeTab === 'assessment' && (
        <div>
          <div className="mb-6 bg-white p-5 rounded-lg border border-gray-200 shadow-sm">
            <h4 className="text-lg font-semibold text-[#0077B6] mb-3">Assessment</h4>
            <p className="text-sm text-red-600 mb-2">
            (WNL: within normal limit &nbsp; RCT: Root canal treatment &nbsp; IAC: Inferior alveolar canal)
            </p>
            {renderRadioButtons(
              'Pulpal',
              formData.assessment.pulpal,
              (e) => handleDirectChange('assessment', 'pulpal', e.target.value),
              ['WNL', 'Reversible Pulpitis', 'Symptomatic Irrev pulpitis', 'Asymptomatic Irrev pulpitis', 'Necrosis', 'Prior RCT / Non healing', 'Previous initiated'],
              errors['assessment.pulpal']
            )}
            {renderRadioButtons(
              'Periapical',
              formData.assessment.periapical,
              (e) => handleDirectChange('assessment', 'periapical', e.target.value),
              ['WNL', 'APP', 'CPP', 'APA', 'CPA', 'Cond Osteitis'],
              errors['assessment.periapical']
            )}
            {renderRadioButtons(
              'Etiology',
              formData.assessment.etiology,
              (e) => handleDirectChange('assessment', 'etiology', e.target.value),
              ['Caries', 'Restoration', 'Prior RCT', 'Iatrogenic', 'Coronal leakage', 'Trauma', 'Perio', 'Others'],
              errors['assessment.etiology']
            )}
            {renderRadioButtons(
              'Prognosis',
              formData.assessment.prognosis,
              (e) => handleDirectChange('assessment', 'prognosis', e.target.value),
              ['Good', 'Fair', 'Poor'],
              errors['assessment.prognosis']
            )}
          </div>
        </div>
      )}

      {activeTab === 'treatmentPlan' && (
        <div>
          <div className="mb-6 bg-white p-5 rounded-lg border border-gray-200 shadow-sm">
            <h4 className="text-lg font-semibold text-[#0077B6] mb-3">Treatment Plan</h4>
            <p className="text-sm text-red-600 mb-2">
            (WNL: within normal limit &nbsp; RCT: Root canal treatment &nbsp; IAC: Inferior alveolar canal)
            </p>
            {renderRadioButtons(
              'Endodontic',
              formData.treatmentPlan.endodontic,
              (e) => handleDirectChange('treatmentPlan', 'endodontic', e.target.value),
              ['RCT', 'Retreatment', 'Incision/ Drainage', 'Apexification/ VPT', 'Apicectomy', 'Perforation/ Resorption repair'],
              errors['treatmentPlan.endodontic']
            )}
            {renderRadioButtons(
              'Periodontal',
              formData.treatmentPlan.periodontal,
              (e) => handleDirectChange('treatmentPlan', 'periodontal', e.target.value),
              ['Scaling', 'Curettage', 'Crown lengthening', 'Root amputation', 'Hemisection', 'Extraction'],
              errors['treatmentPlan.periodontal']
            )}
            {renderRadioButtons(
              'Restorative',
              formData.treatmentPlan.restorative,
              (e) => handleDirectChange('treatmentPlan', 'restorative', e.target.value),
              ['Temporary', 'Post/core', 'Composite', 'Onlay/ Crown', 'Bleaching'],
              errors['treatmentPlan.restorative']
            )}
          </div>
        </div>
      )}

      <div className="mt-8 flex justify-end gap-4">
        <button
          onClick={handleDownloadPDF}
          className="px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white font-medium rounded-lg hover:shadow-lg transition-all duration-300 flex items-center"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 mr-2"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
          Download PDF
        </button>
        <button
          onClick={handleSubmit}
          className="px-6 py-2 bg-[#0077B6] text-white font-medium rounded-lg hover:bg-[#005f92] transition-all shadow-sm"
        >
          Save Sheet
        </button>
      </div>
    </motion.div>
  );
};

export default EndodonticSheet;