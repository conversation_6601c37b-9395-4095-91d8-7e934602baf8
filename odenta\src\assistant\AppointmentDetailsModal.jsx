import { motion, AnimatePresence } from 'framer-motion';
import { FaTimes, FaCalendarAlt, FaClock, FaUser, FaClipboardList, FaInfoCircle, FaUserMd, FaPhone, FaIdCard, FaMapMarkerAlt, FaBriefcase } from 'react-icons/fa';

// Website color palette
const colorPalette = {
  primary: '#0077B6',
  secondary: '#20B2AA',
  background: '#FFFFFF',
  text: '#333333',
  accent: '#28A745'
};

const AppointmentDetailsModal = ({ isOpen, onClose, appointment }) => {
  if (!appointment) return null;

  // Format date to a readable format
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50 p-4"
          onClick={onClose}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0, y: 20 }}
            animate={{ scale: 1, opacity: 1, y: 0 }}
            exit={{ scale: 0.9, opacity: 0, y: 20 }}
            transition={{ type: "spring", damping: 25, stiffness: 300 }}
            className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto relative"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header with appointment type */}
            <div className="sticky top-0 z-10 text-white p-6 rounded-t-2xl flex justify-between items-center"
              style={{ background: `linear-gradient(to right, ${colorPalette.primary}, ${colorPalette.secondary})` }}>
              <div className="flex items-center">
                <FaCalendarAlt className="h-6 w-6 mr-3" />
                <h2 className="text-xl font-bold">{appointment.type || 'Appointment'} Details</h2>
              </div>
              <button
                onClick={onClose}
                className="text-white hover:text-gray-200 transition-colors"
              >
                <FaTimes className="h-6 w-6" />
              </button>
            </div>

            {/* Main content */}
            <div className="p-6 space-y-6">
              {/* Quick Info Cards */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="bg-white p-4 rounded-xl shadow-sm border border-gray-100 flex items-start">
                  <div className="p-3 rounded-lg mr-3" style={{ backgroundColor: `${colorPalette.primary}15` }}>
                    <FaCalendarAlt style={{ color: colorPalette.primary }} />
                  </div>
                  <div>
                    <h4 className="text-sm font-semibold text-gray-500">Date</h4>
                    <p className="text-gray-800">{formatDate(appointment.date)}</p>
                  </div>
                </div>

                <div className="bg-white p-4 rounded-xl shadow-sm border border-gray-100 flex items-start">
                  <div className="p-3 rounded-lg mr-3" style={{ backgroundColor: `${colorPalette.primary}15` }}>
                    <FaClock style={{ color: colorPalette.primary }} />
                  </div>
                  <div>
                    <h4 className="text-sm font-semibold text-gray-500">Time</h4>
                    <p className="text-gray-800">{appointment.time || 'N/A'}</p>
                  </div>
                </div>
              </div>

              {/* Patient Information */}
              <section className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 flex items-center" style={{ color: colorPalette.primary }}>
                  <FaUser className="mr-2" style={{ color: colorPalette.primary }} /> Patient Information
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-start">
                    <FaIdCard className="mt-1 mr-2" style={{ color: colorPalette.primary }} />
                    <div>
                      <p className="text-sm font-medium text-gray-500">Name</p>
                      <p className="text-gray-800">{appointment.patient?.fullName || appointment.fullName || 'N/A'}</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <FaIdCard className="mt-1 mr-2" style={{ color: colorPalette.primary }} />
                    <div>
                      <p className="text-sm font-medium text-gray-500">National ID</p>
                      <p className="text-gray-800 font-semibold">
                        {(() => {
                          // Log the appointment data to see what's available
                          console.log('Appointment data in modal:', {
                            appointmentId: appointment._id,
                            patientObj: appointment.patient,
                            nationalIdFromPatient: appointment.patient?.nationalId,
                            nationalIdDirect: appointment.nationalId
                          });

                          // Try to get the national ID from various sources
                          // First check if it's directly on the appointment object
                          if (appointment.nationalId) {
                            return appointment.nationalId;
                          }

                          // Then check if it's in the patient object
                          if (appointment.patient && appointment.patient.nationalId) {
                            return appointment.patient.nationalId;
                          }

                          return 'N/A';
                        })()}
                      </p>
                    </div>
                  </div>

                  {(appointment.phoneNumber || appointment.patient?.phoneNumber) && (
                    <div className="flex items-start">
                      <FaPhone className="mt-1 mr-2" style={{ color: colorPalette.primary }} />
                      <div>
                        <p className="text-sm font-medium text-gray-500">Phone</p>
                        <p className="text-gray-800">{appointment.phoneNumber || appointment.patient?.phoneNumber}</p>
                      </div>
                    </div>
                  )}

                  {(appointment.age || appointment.patient?.age) && (
                    <div className="flex items-start">
                      <FaInfoCircle className="mt-1 mr-2" style={{ color: colorPalette.primary }} />
                      <div>
                        <p className="text-sm font-medium text-gray-500">Age</p>
                        <p className="text-gray-800">{appointment.age || appointment.patient?.age}</p>
                      </div>
                    </div>
                  )}

                  {appointment.occupation && (
                    <div className="flex items-start">
                      <FaBriefcase className="mt-1 mr-2" style={{ color: colorPalette.primary }} />
                      <div>
                        <p className="text-sm font-medium text-gray-500">Occupation</p>
                        <p className="text-gray-800">{appointment.occupation}</p>
                      </div>
                    </div>
                  )}

                  {appointment.address && (
                    <div className="flex items-start">
                      <FaMapMarkerAlt className="mt-1 mr-2" style={{ color: colorPalette.primary }} />
                      <div>
                        <p className="text-sm font-medium text-gray-500">Address</p>
                        <p className="text-gray-800">{appointment.address}</p>
                      </div>
                    </div>
                  )}
                </div>
              </section>

              {/* Appointment Details */}
              <section className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 flex items-center" style={{ color: colorPalette.primary }}>
                  <FaClipboardList className="mr-2" style={{ color: colorPalette.primary }} /> Appointment Details
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-start">
                    <FaInfoCircle className="mt-1 mr-2" style={{ color: colorPalette.primary }} />
                    <div>
                      <p className="text-sm font-medium text-gray-500">Type</p>
                      <p className="text-gray-800">{appointment.type || appointment.treatment || 'N/A'}</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <FaInfoCircle className="mt-1 mr-2" style={{ color: colorPalette.primary }} />
                    <div>
                      <p className="text-sm font-medium text-gray-500">Status</p>
                      <p style={{
                        color: appointment.status === 'completed' ? colorPalette.accent :
                              appointment.status === 'cancelled' ? '#ef4444' :
                              colorPalette.primary,
                        fontWeight: 'medium'
                      }}>
                        {appointment.status}
                      </p>
                    </div>
                  </div>

                  {appointment.chiefComplaint && (
                    <div className="flex items-start col-span-2">
                      <FaInfoCircle className="mt-1 mr-2" style={{ color: colorPalette.primary }} />
                      <div>
                        <p className="text-sm font-medium text-gray-500">Chief Complaint</p>
                        <p className="text-gray-800">{appointment.chiefComplaint}</p>
                      </div>
                    </div>
                  )}

                  {appointment.notes && (
                    <div className="flex items-start col-span-2">
                      <FaInfoCircle className="mt-1 mr-2" style={{ color: colorPalette.primary }} />
                      <div>
                        <p className="text-sm font-medium text-gray-500">Notes</p>
                        <p className="text-gray-800">{appointment.notes}</p>
                      </div>
                    </div>
                  )}
                </div>
              </section>

              {/* Student Information */}
              <section className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                <h3 className="text-lg font-semibold mb-4 flex items-center" style={{ color: colorPalette.primary }}>
                  <FaUserMd className="mr-2" style={{ color: colorPalette.primary }} /> Student Information
                </h3>
                {appointment.doctor && appointment.doctorModel === 'Student' ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-start">
                      <FaUserMd className="mt-1 mr-2" style={{ color: colorPalette.primary }} />
                      <div>
                        <p className="text-sm font-medium text-gray-500">Student Name</p>
                        <p className="text-gray-800">{appointment.studentName || 'Assigned'}</p>
                      </div>
                    </div>

                    <div className="flex items-start">
                      <FaIdCard className="mt-1 mr-2" style={{ color: colorPalette.primary }} />
                      <div>
                        <p className="text-sm font-medium text-gray-500">Student ID</p>
                        <p className="text-gray-800">{appointment.studentId || appointment.doctor}</p>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center justify-center p-4 bg-gray-50 rounded-lg">
                    <p className="text-gray-500">No student assigned to this appointment</p>
                  </div>
                )}
              </section>
            </div>

            {/* Footer with close button */}
            <div className="sticky bottom-0 bg-white p-6 border-t rounded-b-2xl flex justify-end">
              <button
                onClick={onClose}
                className="px-6 py-2 text-white rounded-lg transition-colors shadow-md hover:shadow-lg font-medium hover:brightness-110"
                style={{
                  background: `linear-gradient(to right, ${colorPalette.primary}, ${colorPalette.secondary})`
                }}
              >
                Close
              </button>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default AppointmentDetailsModal;
