import { motion } from 'framer-motion';
import { FaCheckCircle, FaTimes } from 'react-icons/fa';

const SuccessModal = ({ isOpen, onClose, title, message }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        className="bg-white rounded-2xl shadow-2xl w-full max-w-md p-6"
      >
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-blue-900">{title}</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <FaTimes className="h-5 w-5" />
          </button>
        </div>
        
        <div className="flex flex-col items-center justify-center py-4">
          <div className="bg-green-100 rounded-full p-3 mb-4">
            <FaCheckCircle className="h-10 w-10 text-green-600" />
          </div>
          <p className="text-gray-700 text-center mb-6">{message}</p>
        </div>
        
        <div className="flex justify-center">
          <motion.button
            onClick={onClose}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium transition-colors shadow-md hover:shadow-lg"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            OK
          </motion.button>
        </div>
      </motion.div>
    </div>
  );
};

export default SuccessModal;
