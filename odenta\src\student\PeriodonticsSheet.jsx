import { useState } from 'react';
import { motion } from 'framer-motion';
import MedicalTab from './MedicalTab';
import { generateSheetPDF } from '../utils/pdfUtils';

const PeriodonticsSheet = ({ initialData, onSave }) => {
  const defaultFormData = {
    generalInformation: {
      pastDentalHistory: {
        history: '',
        numberOfExtractedTeeth: ''
      },
      familyHistory: '',
      habits: {
        smoking: '',
        toothBrushing: '',
        parafunctional: ''
      },
      extraOralExamination: ''
    },
    intraOralExamination: {
      periodontalFindings: {
        gingivalCriteria: {
          colour: '',
          shapeContour: '',
          texture: '',
          size: '',
          consistency: '',
          exudates: '',
          furcations: '',
          teeth: '',
          functionalRelations: '',
          percussion: ''
        },
        gingivalBleedingIndex: {
          bleedingSites: Array(32).fill({ topLeft: 'none', topRight: 'none', bottomLeft: 'none', bottomRight: 'none' }),
          totalScore: 0
        }
      }
    },
    mucogingivalDeformities: {
      periodontalBiotype: '',
      keratinizedGingiva: '',
      vestibularDepth: '',
      frenumAttachment: '',
      gingivalExcess: '',
      abnormalColor: '',
      recession: [
        {
          affectedToothNumber: '',
          gingivalSite: '',
          toothSite: '',
          rt: '',
          cairoEtal: '',
          rec: '',
          depth: '',
          gt: '',
          ktw: '',
          cej: '',
          step: ''
        }
      ]
    },
    radiographicExamination: {
      bonyLossHorizontal: '',
      bonyLossVertical: '',
      furcationInvolvement: '',
      laminaDuraCrestal: '',
      laminaDuraOverall: '',
      pdlSpace: '',
      boneLossPercentage: '',
      others: ''
    },
    diagnosisAndTreatment: {
      gingivalDiseases: '',
      periodontalDiseases: [
        {
          stage: '',
          grade: '',
          gradeModifiers: '',
          distribution: ''
        }
      ],
      finalDiagnosis: '',
      otherDiagnosis: '',
      factors: {
        local: '',
        functional: '',
        systemic: '',
        environmental: ''
      },
      treatmentPlan: {
        emergencyPhase: '',
        etiotropicPhase: {
          limitedPlaqueControl: false,
          limitedPlaqueSite: '',
          supragingivalScaling: false,
          supragingivalScalingSite: '',
          correctionDefectiveRestorations: false,
          correctionDefectiveRestorationsSite: '',
          obturationCaries: false,
          obturationCariesSite: '',
          subgingivalScaling: false,
          subgingivalScalingSite: '',
          comprehensivePlaqueControl: false,
          comprehensivePlaqueControlSite: '',
          coronoplasty: false,
          coronoplastySite: '',
          minorOrthodontic: false,
          minorOrthodonticSite: '',
          tissueReevaluation: false,
          tissueReevaluationSite: ''
        },
        surgicalPhase: '',
        restorativePhase: '',
        maintenancePhase: '',
        consultation: '',
        recallMaintenance: ''
      }
    },
    charting: {
      upper: {
        labial: {
          M: Array(16).fill(''),
          F: Array(16).fill(''),
          CAL: Array(16).fill(['', '', '', '', '', '']),
          PD: Array(16).fill(['', '', '', '', '', ''])
        },
        palatal: {
          M: Array(16).fill(''),
          F: Array(16).fill(''),
          CAL: Array(16).fill(['', '', '', '', '', '']),
          PD: Array(16).fill(['', '', '', '', '', ''])
        }
      },
      lower: {
        buccal: {
          M: Array(16).fill(''),
          F: Array(16).fill(''),
          CAL: Array(16).fill(['', '', '', '', '', '']),
          PD: Array(16).fill(['', '', '', '', '', ''])
        },
        lingual: {
          M: Array(16).fill(''),
          F: Array(16).fill(''),
          CAL: Array(16).fill(['', '', '', '', '', '']),
          PD: Array(16).fill(['', '', '', '', '', ''])
        }
      }
    }
  };

  const mergedData = initialData && Object.keys(initialData).length > 0
    ? { ...defaultFormData, ...initialData }
    : defaultFormData;

  const [formData, setFormData] = useState(mergedData);
  const [errors, setErrors] = useState({});
  const [activeTab, setActiveTab] = useState('medical');
  const [xrayFile, setXrayFile] = useState(null);

  const fdiTeethNumbers = [
    '18', '17', '16', '15', '14', '13', '12', '11',
    '21', '22', '23', '24', '25', '26', '27', '28',
    '38', '37', '36', '35', '34', '33', '32', '31',
    '41', '42', '43', '44', '45', '46', '47', '48'
  ];

  const upperTeethNumbers = fdiTeethNumbers.slice(0, 16);
  const lowerTeethNumbers = fdiTeethNumbers.slice(16);

  // All fields are optional in this form
  const validateForm = () => {
    // Clear any existing errors
    setErrors({});
    return true;
  };

  const handleChange = (section, field, value) => {
    setFormData((prev) => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
    setErrors((prev) => ({ ...prev, [`${section}.${field}`]: '' }));
  };

  const handleNestedChange = (section, subsection, value) => {
    setFormData((prev) => ({
      ...prev,
      [section]: {
        ...prev[section],
        [subsection]: value
      }
    }));
    setErrors((prev) => ({ ...prev, [`${section}.${subsection}`]: '' }));
  };



  const handleRecessionRowChange = (section, subsection, rowIndex, field, value) => {
    setFormData((prev) => {
      const updatedRows = [...prev[section][subsection]];
      updatedRows[rowIndex] = {
        ...updatedRows[rowIndex],
        [field]: value
      };
      return {
        ...prev,
        [section]: {
          ...prev[section],
          [subsection]: updatedRows
        }
      };
    });
    setErrors((prev) => ({ ...prev, [`${section}.${subsection}.${rowIndex}.${field}`]: '' }));
  };

  const handleAddRecessionRow = (section, subsection) => {
    setFormData((prev) => ({
      ...prev,
      [section]: {
        ...prev[section],
        [subsection]: [
          ...prev[section][subsection],
          {
            affectedToothNumber: '',
            gingivalSite: '',
            toothSite: '',
            rt: '',
            cairoEtal: '',
            rec: '',
            depth: '',
            gt: '',
            ktw: '',
            cej: '',
            step: ''
          }
        ]
      }
    }));
  };

  const handleRemoveRecessionRow = (section, subsection, rowIndex) => {
    setFormData((prev) => ({
      ...prev,
      [section]: {
        ...prev[section],
        [subsection]: prev[section][subsection].filter((_, index) => index !== rowIndex)
      }
    }));
  };

  const handleChartingChange = (arch, view, field, index, subIndex, value) => {
    setFormData((prev) => {
      const updatedField = [...prev.charting[arch][view][field]];
      if (Array.isArray(updatedField[index])) {
        updatedField[index] = [...updatedField[index]];
        updatedField[index][subIndex] = value;
      } else {
        updatedField[index] = value;
      }
      return {
        ...prev,
        charting: {
          ...prev.charting,
          [arch]: {
            ...prev.charting[arch],
            [view]: {
              ...prev.charting[arch][view],
              [field]: updatedField
            }
          }
        }
      };
    });
  };

  const handleBleedingSiteToggle = (toothIndex, site) => {
    setFormData((prev) => {
      const updatedBleedingSites = [...prev.intraOralExamination.periodontalFindings.gingivalBleedingIndex.bleedingSites];

      // Toggle between states: none -> pink -> red -> none
      let currentState = updatedBleedingSites[toothIndex][site];
      let newState;

      if (currentState === 'none' || !currentState) {
        newState = 'pink';
      } else if (currentState === 'pink') {
        newState = 'red';
      } else {
        newState = 'none';
      }

      updatedBleedingSites[toothIndex] = {
        ...updatedBleedingSites[toothIndex],
        [site]: newState
      };

      const totalPositiveSites = updatedBleedingSites.reduce((sum, tooth) => {
        return sum +
          (tooth.topLeft && tooth.topLeft !== 'none' ? 1 : 0) +
          (tooth.topRight && tooth.topRight !== 'none' ? 1 : 0) +
          (tooth.bottomLeft && tooth.bottomLeft !== 'none' ? 1 : 0) +
          (tooth.bottomRight && tooth.bottomRight !== 'none' ? 1 : 0);
      }, 0);

      const totalSites = updatedBleedingSites.length * 4;
      const score = (totalPositiveSites / totalSites) * 100;

      return {
        ...prev,
        intraOralExamination: {
          ...prev.intraOralExamination,
          periodontalFindings: {
            ...prev.intraOralExamination.periodontalFindings,
            gingivalBleedingIndex: {
              bleedingSites: updatedBleedingSites,
              totalScore: score
            }
          }
        }
      };
    });
  };

  const handleAddPeriodontalDiseaseRow = (section, subsection) => {
    setFormData((prev) => ({
      ...prev,
      [section]: {
        ...prev[section],
        [subsection]: [
          ...prev[section][subsection],
          {
            stage: '',
            grade: '',
            gradeModifiers: '',
            distribution: ''
          }
        ]
      }
    }));
  };

  const handleRemovePeriodontalDiseaseRow = (section, subsection, rowIndex) => {
    setFormData((prev) => ({
      ...prev,
      [section]: {
        ...prev[section],
        [subsection]: prev[section][subsection].filter((_, index) => index !== rowIndex)
      }
    }));
  };

  const handlePeriodontalDiseaseRowChange = (section, subsection, rowIndex, field, value) => {
    setFormData((prev) => {
      const updatedRows = [...prev[section][subsection]];
      updatedRows[rowIndex] = {
        ...updatedRows[rowIndex],
        [field]: value
      };
      return {
        ...prev,
        [section]: {
          ...prev[section],
          [subsection]: updatedRows
        }
      };
    });
    setErrors((prev) => ({ ...prev, [`${section}.${subsection}.${rowIndex}.${field}`]: '' }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (validateForm()) {
      try {
        // Extract diagnosis and treatment plan from the form data
        // Ensure diagnosis is never empty by providing a default value
        let diagnosis = formData.diagnosisAndTreatment.finalDiagnosis || 'Periodontal Examination';

        if (formData.diagnosisAndTreatment.periodontalDiseases &&
            formData.diagnosisAndTreatment.periodontalDiseases.length > 0) {
          // Add periodontal diseases to diagnosis if available
          const perioDisease = formData.diagnosisAndTreatment.periodontalDiseases[0];
          if (perioDisease.stage || perioDisease.grade) {
            diagnosis += ` - Stage ${perioDisease.stage || 'N/A'}, Grade ${perioDisease.grade || 'N/A'}`;
          }
        }

        // Process treatment plan - ensure it's never empty
        let treatmentPlanEntries = Object.entries(formData.diagnosisAndTreatment.treatmentPlan)
          .filter(([_, value]) => value && typeof value === 'string') // Filter only string values
          .map(([key, value]) => `${key.replace(/([A-Z])/g, ' $1').trim()}: ${value}`); // Format each entry

        // Add etiotropic phase checked items
        const etiotropicItems = Object.entries(formData.diagnosisAndTreatment.treatmentPlan.etiotropicPhase)
          .filter(([key, value]) => value === true && !key.includes('Site'))
          .map(([key]) => `- ${key.replace(/([A-Z])/g, ' $1').trim()}`);

        if (etiotropicItems.length > 0) {
          treatmentPlanEntries.push('Etiotropic Phase:');
          treatmentPlanEntries = treatmentPlanEntries.concat(etiotropicItems);
        }

        // Ensure treatment plan is never empty
        const treatmentPlan = treatmentPlanEntries.length > 0
          ? treatmentPlanEntries.join('\n')
          : 'Periodontal Treatment Plan';

        // Notes can include additional information
        const notes = `Gingival Diseases: ${formData.diagnosisAndTreatment.gingivalDiseases || 'None'}\nOther Diagnosis: ${formData.diagnosisAndTreatment.otherDiagnosis || 'None'}`;

        // Call the onSave function from props
        if (onSave) {
          const success = await onSave(formData, diagnosis, treatmentPlan, notes);
          if (success) {
            console.log('Periodontics sheet saved successfully');
          } else {
            alert('Failed to save sheet. Please try again.');
          }
        } else {
          console.log('Form submitted:', formData);
          alert('Form submitted successfully!');
        }
      } catch (error) {
        console.error('Error saving sheet:', error);
        alert('An error occurred while saving the sheet. Please try again.');
      }
    } else {
      console.log('Validation failed:', errors);
      alert('Please fix the errors before submitting.');
    }
  };

  const handleDownloadPDF = async () => {
    try {
      // Extract diagnosis and treatment plan from the form data
      let diagnosis = formData.diagnosisAndTreatment.finalDiagnosis || 'Periodontal Examination';

      if (formData.diagnosisAndTreatment.periodontalDiseases &&
          formData.diagnosisAndTreatment.periodontalDiseases.length > 0) {
        // Add periodontal diseases to diagnosis if available
        const perioDisease = formData.diagnosisAndTreatment.periodontalDiseases[0];
        if (perioDisease.stage || perioDisease.grade) {
          diagnosis += ` - Stage ${perioDisease.stage || 'N/A'}, Grade ${perioDisease.grade || 'N/A'}`;
        }
      }

      // Build treatment plan from various sections
      const treatmentPlanEntries = [];
      if (formData.diagnosisAndTreatment.treatmentPlan?.phase1) {
        treatmentPlanEntries.push(`Phase 1: ${formData.diagnosisAndTreatment.treatmentPlan.phase1}`);
      }
      if (formData.diagnosisAndTreatment.treatmentPlan?.phase2) {
        treatmentPlanEntries.push(`Phase 2: ${formData.diagnosisAndTreatment.treatmentPlan.phase2}`);
      }
      if (formData.diagnosisAndTreatment.treatmentPlan?.phase3) {
        treatmentPlanEntries.push(`Phase 3: ${formData.diagnosisAndTreatment.treatmentPlan.phase3}`);
      }
      if (formData.diagnosisAndTreatment.treatmentPlan?.phase4) {
        treatmentPlanEntries.push(`Phase 4: ${formData.diagnosisAndTreatment.treatmentPlan.phase4}`);
      }

      // Ensure treatment plan is never empty
      const treatmentPlan = treatmentPlanEntries.length > 0
        ? treatmentPlanEntries.join('\n')
        : 'Periodontal Treatment Plan';

      // Notes can include additional information
      const notes = `Gingival Diseases: ${formData.diagnosisAndTreatment.gingivalDiseases || 'None'}\nOther Diagnosis: ${formData.diagnosisAndTreatment.otherDiagnosis || 'None'}`;

      // Create a mock sheet object for PDF generation
      const mockSheet = {
        type: 'Periodontics',
        createdAt: new Date().toISOString(),
        details: {
          diagnosis,
          treatmentPlan,
          notes,
          specificData: formData
        }
      };

      await generateSheetPDF(mockSheet, 'Current_Patient');
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Failed to generate PDF. Please try again.');
    }
  };



  const handleXrayUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      setXrayFile(file);
    }
  };

  const renderGeneralInformation = () => (
    <motion.div variants={item} className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-700 mb-2">Past Dental History</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-base font-medium text-gray-600">History</label>
            <textarea
              value={formData.generalInformation.pastDentalHistory.history}
              onChange={(e) => {
                const updatedPastDentalHistory = {
                  ...formData.generalInformation.pastDentalHistory,
                  history: e.target.value
                };
                handleNestedChange('generalInformation', 'pastDentalHistory', updatedPastDentalHistory);
              }}
              className="mt-1 block w-full border border-gray-300 rounded-md p-2 focus:ring-blue-500 focus:border-blue-500"
              rows="3"
            />
            {errors['generalInformation.pastDentalHistory.history'] && (
              <p className="text-red-500 text-sm mt-1">{errors['generalInformation.pastDentalHistory.history']}</p>
            )}
          </div>
          <div>
            <label className="block text-base font-medium text-gray-600">Number of Extracted Teeth</label>
            <input
              type="number"
              value={formData.generalInformation.pastDentalHistory.numberOfExtractedTeeth}
              onChange={(e) => {
                const updatedPastDentalHistory = {
                  ...formData.generalInformation.pastDentalHistory,
                  numberOfExtractedTeeth: e.target.value
                };
                handleNestedChange('generalInformation', 'pastDentalHistory', updatedPastDentalHistory);
              }}
              className="mt-1 block w-full border border-gray-300 rounded-md p-2 focus:ring-blue-500 focus:border-blue-500"
            />
            {errors['generalInformation.pastDentalHistory.numberOfExtractedTeeth'] && (
              <p className="text-red-500 text-sm mt-1">{errors['generalInformation.pastDentalHistory.numberOfExtractedTeeth']}</p>
            )}
          </div>
        </div>
      </div>
      <div>
        <h3 className="text-lg font-semibold text-gray-700 mb-2">Family History</h3>
        <textarea
          value={formData.generalInformation.familyHistory || ''}
          onChange={(e) => handleNestedChange('generalInformation', 'familyHistory', e.target.value)}
          className="mt-1 block w-full border border-gray-300 rounded-md p-2 focus:ring-blue-500 focus:border-blue-500"
          rows="3"
        />
        {errors['generalInformation.familyHistory'] && (
          <p className="text-red-500 text-sm mt-1">{errors['generalInformation.familyHistory']}</p>
        )}
      </div>
      <div>
        <h3 className="text-lg font-semibold text-gray-700 mb-2">Habits</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-base font-medium text-gray-600">Smoking</label>
            <input
              type="text"
              value={formData.generalInformation.habits.smoking}
              onChange={(e) => {
                const updatedHabits = {
                  ...formData.generalInformation.habits,
                  smoking: e.target.value
                };
                handleNestedChange('generalInformation', 'habits', updatedHabits);
              }}
              className="mt-1 block w-full border border-gray-300 rounded-md p-2 focus:ring-blue-500 focus:border-blue-500"
            />
            {errors['generalInformation.habits.smoking'] && (
              <p className="text-red-500 text-sm mt-1">{errors['generalInformation.habits.smoking']}</p>
            )}
          </div>
          <div>
            <label className="block text-base font-medium text-gray-600">Tooth Brushing</label>
            <input
              type="text"
              value={formData.generalInformation.habits.toothBrushing}
              onChange={(e) => {
                const updatedHabits = {
                  ...formData.generalInformation.habits,
                  toothBrushing: e.target.value
                };
                handleNestedChange('generalInformation', 'habits', updatedHabits);
              }}
              className="mt-1 block w-full border border-gray-300 rounded-md p-2 focus:ring-blue-500 focus:border-blue-500"
            />
            {errors['generalInformation.habits.toothBrushing'] && (
              <p className="text-red-500 text-sm mt-1">{errors['generalInformation.habits.toothBrushing']}</p>
            )}
          </div>
          <div>
            <label className="block text-base font-medium text-gray-600">Parafunctional</label>
            <input
              type="text"
              value={formData.generalInformation.habits.parafunctional}
              onChange={(e) => {
                const updatedHabits = {
                  ...formData.generalInformation.habits,
                  parafunctional: e.target.value
                };
                handleNestedChange('generalInformation', 'habits', updatedHabits);
              }}
              className="mt-1 block w-full border border-gray-300 rounded-md p-2 focus:ring-blue-500 focus:border-blue-500"
            />
            {errors['generalInformation.habits.parafunctional'] && (
              <p className="text-red-500 text-sm mt-1">{errors['generalInformation.habits.parafunctional']}</p>
            )}
          </div>
        </div>
      </div>
      <div>
        <h3 className="text-lg font-semibold text-gray-700 mb-2">Extra-Oral Examination</h3>
        <textarea
          value={formData.generalInformation.extraOralExamination || ''}
          onChange={(e) => handleNestedChange('generalInformation', 'extraOralExamination', e.target.value)}
          className="mt-1 block w-full border border-gray-300 rounded-md p-2 focus:ring-blue-500 focus:border-blue-500"
          rows="3"
        />
        {errors['generalInformation.extraOralExamination'] && (
          <p className="text-red-500 text-sm mt-1">{errors['generalInformation.extraOralExamination']}</p>
        )}
      </div>
    </motion.div>
  );

  const renderIntraOralExamination = () => (
    <motion.div variants={item} className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-700 mb-2">Periodontal Findings</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Object.keys(formData.intraOralExamination.periodontalFindings.gingivalCriteria).map((field) => (
            <div key={field}>
              <label className="block text-sm font-medium text-gray-600 capitalize">{field.replace(/([A-Z])/g, ' $1')}</label>
              <input
                type="text"
                value={formData.intraOralExamination.periodontalFindings.gingivalCriteria[field] || ''}
                onChange={(e) => {
                  const updatedGingivalCriteria = {
                    ...formData.intraOralExamination.periodontalFindings.gingivalCriteria,
                    [field]: e.target.value
                  };
                  const updatedPeriodontalFindings = {
                    ...formData.intraOralExamination.periodontalFindings,
                    gingivalCriteria: updatedGingivalCriteria
                  };
                  handleNestedChange('intraOralExamination', 'periodontalFindings', updatedPeriodontalFindings);
                }}
                className="mt-1 block w-full border border-gray-300 rounded-md p-2 focus:ring-blue-500 focus:border-blue-500"
              />
              {errors[`intraOralExamination.periodontalFindings.gingivalCriteria.${field}`] && (
                <p className="text-red-500 text-sm mt-1">{errors[`intraOralExamination.periodontalFindings.gingivalCriteria.${field}`]}</p>
              )}
            </div>
          ))}
        </div>
      </div>
      <div className="mt-4">
        <h5 className="text-base font-medium text-gray-600 mb-2">Gingival Bleeding Index (+/-)</h5>
        <div className="space-y-6 p-6 bg-[#f3f4f6] rounded-lg">
          {/* Upper Teeth */}
          <div className="mb-6">
            <h6 className="text-sm font-medium text-gray-600 mb-4">Upper Teeth</h6>
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(8, minmax(0, 1fr))', gap: '16px' }}>
              {formData.intraOralExamination.periodontalFindings.gingivalBleedingIndex.bleedingSites.slice(0, 16).map((tooth, index) => (
                <div key={index} style={{ position: 'relative', width: '80px', height: '160px' }}>
                  <div style={{ marginBottom: '15px', textAlign: 'center' }}>
                    <img
                      src={`${process.env.PUBLIC_URL}/imgs/teeth/${fdiTeethNumbers[index]}.png`}
                      alt={`Tooth ${fdiTeethNumbers[index]}`}
                      className="w-12 h-12 object-contain mx-auto"
                    />
                  </div>
                  <div style={{ textAlign: 'center', marginBottom: '20px', fontSize: '14px', fontWeight: 'bold', color: '#1f2937' }}>
                    {fdiTeethNumbers[index]}
                  </div>
                  <svg width="60" height="60" viewBox="0 0 60 60" style={{ margin: '0 auto', display: 'block' }}>
                    <rect x="0" y="0" width="60" height="60" fill="white" stroke="black" strokeWidth="1" />
                    <line x1="30" y1="0" x2="30" y2="60" stroke="black" strokeWidth="1" />
                    <line x1="0" y1="30" x2="60" y2="30" stroke="black" strokeWidth="1" />
                    <polygon
                      points="0,0 60,0 30,30"
                      fill={tooth.topLeft === 'pink' ? '#FFC0CB' : tooth.topLeft === 'red' ? '#FF0000' : 'white'}
                      stroke="black"
                      strokeWidth="1"
                      onClick={() => handleBleedingSiteToggle(index, 'topLeft')}
                      style={{ cursor: 'pointer' }}
                    />
                    <polygon
                      points="60,0 60,60 30,30"
                      fill={tooth.topRight === 'pink' ? '#FFC0CB' : tooth.topRight === 'red' ? '#FF0000' : 'white'}
                      stroke="black"
                      strokeWidth="1"
                      onClick={() => handleBleedingSiteToggle(index, 'topRight')}
                      style={{ cursor: 'pointer' }}
                    />
                    <polygon
                      points="0,60 60,60 30,30"
                      fill={tooth.bottomRight === 'pink' ? '#FFC0CB' : tooth.bottomRight === 'red' ? '#FF0000' : 'white'}
                      stroke="black"
                      strokeWidth="1"
                      onClick={() => handleBleedingSiteToggle(index, 'bottomRight')}
                      style={{ cursor: 'pointer' }}
                    />
                    <polygon
                      points="0,0 0,60 30,30"
                      fill={tooth.bottomLeft === 'pink' ? '#FFC0CB' : tooth.bottomLeft === 'red' ? '#FF0000' : 'white'}
                      stroke="black"
                      strokeWidth="1"
                      onClick={() => handleBleedingSiteToggle(index, 'bottomLeft')}
                      style={{ cursor: 'pointer' }}
                    />
                  </svg>
                </div>
              ))}
            </div>
          </div>

          {/* Lower Teeth */}
          <div className="mt-8">
            <h6 className="text-sm font-medium text-gray-600 mb-4">Lower Teeth</h6>
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(8, minmax(0, 1fr))', gap: '16px' }}>
              {formData.intraOralExamination.periodontalFindings.gingivalBleedingIndex.bleedingSites.slice(16, 32).map((tooth, index) => (
                <div key={index} style={{ position: 'relative', width: '80px', height: '160px' }}>
                  <div style={{ marginBottom: '15px', textAlign: 'center' }}>
                    <img
                      src={`${process.env.PUBLIC_URL}/imgs/teeth/${fdiTeethNumbers[index + 16]}.png`}
                      alt={`Tooth ${fdiTeethNumbers[index + 16]}`}
                      className="w-12 h-12 object-contain mx-auto"
                    />
                  </div>
                  <div style={{ textAlign: 'center', marginBottom: '20px', fontSize: '14px', fontWeight: 'bold', color: '#1f2937' }}>
                    {fdiTeethNumbers[index + 16]}
                  </div>
                  <svg width="60" height="60" viewBox="0 0 60 60" style={{ margin: '0 auto', display: 'block' }}>
                    <rect x="0" y="0" width="60" height="60" fill="white" stroke="black" strokeWidth="1" />
                    <line x1="30" y1="0" x2="30" y2="60" stroke="black" strokeWidth="1" />
                    <line x1="0" y1="30" x2="60" y2="30" stroke="black" strokeWidth="1" />
                    <polygon
                      points="0,0 60,0 30,30"
                      fill={tooth.topLeft === 'pink' ? '#FFC0CB' : tooth.topLeft === 'red' ? '#FF0000' : 'white'}
                      stroke="black"
                      strokeWidth="1"
                      onClick={() => handleBleedingSiteToggle(index + 16, 'topLeft')}
                      style={{ cursor: 'pointer' }}
                    />
                    <polygon
                      points="60,0 60,60 30,30"
                      fill={tooth.topRight === 'pink' ? '#FFC0CB' : tooth.topRight === 'red' ? '#FF0000' : 'white'}
                      stroke="black"
                      strokeWidth="1"
                      onClick={() => handleBleedingSiteToggle(index + 16, 'topRight')}
                      style={{ cursor: 'pointer' }}
                    />
                    <polygon
                      points="0,60 60,60 30,30"
                      fill={tooth.bottomRight === 'pink' ? '#FFC0CB' : tooth.bottomRight === 'red' ? '#FF0000' : 'white'}
                      stroke="black"
                      strokeWidth="1"
                      onClick={() => handleBleedingSiteToggle(index + 16, 'bottomRight')}
                      style={{ cursor: 'pointer' }}
                    />
                    <polygon
                      points="0,0 0,60 30,30"
                      fill={tooth.bottomLeft === 'pink' ? '#FFC0CB' : tooth.bottomLeft === 'red' ? '#FF0000' : 'white'}
                      stroke="black"
                      strokeWidth="1"
                      onClick={() => handleBleedingSiteToggle(index + 16, 'bottomLeft')}
                      style={{ cursor: 'pointer' }}
                    />
                  </svg>
                </div>
              ))}
            </div>
          </div>
        </div>
        <p className="text-sm text-gray-700 mt-2">
          Bleeding Index Score: {(formData.intraOralExamination.periodontalFindings.gingivalBleedingIndex.totalScore || 0).toFixed(2)}%
        </p>
        <div className="mt-4">
          <h6 className="text-base font-medium text-gray-600 mb-2">Key of Bleeding Index:</h6>
          <div className="flex space-x-4 mb-2">
            <div className="flex items-center">
              <div className="w-4 h-4 bg-white border border-gray-300 mr-2"></div>
              <span className="text-sm">None</span>
            </div>
            <div className="flex items-center">
              <div className="w-4 h-4 bg-[#FFC0CB] border border-gray-300 mr-2"></div>
              <span className="text-sm">Pink (Mild)</span>
            </div>
            <div className="flex items-center">
              <div className="w-4 h-4 bg-[#FF0000] border border-gray-300 mr-2"></div>
              <span className="text-sm">Red (Severe)</span>
            </div>
          </div>
          <table className="w-full border-collapse border border-gray-300">
            <thead>
              <tr className="bg-gray-100">
                <th className="border border-gray-300 px-2 py-1 text-sm">Grade</th>
                <th className="border border-gray-300 px-2 py-1 text-sm">Description</th>
                <th className="border border-gray-300 px-2 py-1 text-sm">Correlation</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="border border-gray-300 px-2 py-1 text-sm">0</td>
                <td className="border border-gray-300 px-2 py-1 text-sm">Normal gingiva: coral pink with no inflammation.</td>
                <td className="border border-gray-300 px-2 py-1 text-sm">(-) Negative</td>
              </tr>
              <tr>
                <td className="border border-gray-300 px-2 py-1 text-sm">1</td>
                <td className="border border-gray-300 px-2 py-1 text-sm">Mild inflammation: Slight color change, slight edema. No bleeding on probing.</td>
                <td className="border border-gray-300 px-2 py-1 text-sm">(-) Negative</td>
              </tr>
              <tr>
                <td className="border border-gray-300 px-2 py-1 text-sm">2</td>
                <td className="border border-gray-300 px-2 py-1 text-sm">Moderate inflammation: edema, redness, glazing. Bleeding on probing.</td>
                <td className="border border-gray-300 px-2 py-1 text-sm">(+) Positive</td>
              </tr>
              <tr>
                <td className="border border-gray-300 px-2 py-1 text-sm">3</td>
                <td className="border border-gray-300 px-2 py-1 text-sm">Severe inflammation: marked redness, edema, ulceration, spontaneous bleeding.</td>
                <td className="border border-gray-300 px-2 py-1 text-sm">(+) Positive</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </motion.div>
  );

  const renderMucogingivalDeformities = () => (
    <motion.div variants={item} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {Object.keys(formData.mucogingivalDeformities).map((field) => (
          field !== 'recession' && (
            <div key={field}>
              <label className="block text-base font-medium text-gray-600 capitalize">{field.replace(/([A-Z])/g, ' $1')}</label>
              <input
                type="text"
                value={formData.mucogingivalDeformities[field] || ''}
                onChange={(e) => handleChange('mucogingivalDeformities', field, e.target.value)}
                className="mt-1 block w-full border border-gray-300 rounded-md p-2 focus:ring-blue-500 focus:border-blue-500"
              />
              {errors[`mucogingivalDeformities.${field}`] && (
                <p className="text-red-500 text-sm mt-1">{errors[`mucogingivalDeformities.${field}`]}</p>
              )}
            </div>
          )
        ))}
      </div>
      <div>
        <h3 className="text-lg font-semibold text-gray-700 mb-2">Recession</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full border-collapse">
            <thead>
              <tr>
                {Object.keys(formData.mucogingivalDeformities.recession[0]).map((key) => (
                  <th key={key} className="border px-2 py-1 text-sm capitalize">{key.replace(/([A-Z])/g, ' $1')}</th>
                ))}
                <th className="border px-2 py-1 text-sm">Actions</th>
              </tr>
            </thead>
            <tbody>
              {formData.mucogingivalDeformities.recession.map((row, rowIndex) => (
                <tr key={rowIndex}>
                  {Object.keys(row).map((field) => (
                    <td key={field} className="border px-2 py-1">
                      <input
                        type="text"
                        value={row[field] || ''}
                        onChange={(e) => handleRecessionRowChange('mucogingivalDeformities', 'recession', rowIndex, field, e.target.value)}
                        className="w-full border border-gray-300 rounded-md p-1 focus:ring-blue-500 focus:border-blue-500"
                      />
                      {errors[`mucogingivalDeformities.recession.${rowIndex}.${field}`] && (
                        <p className="text-red-500 text-xs mt-1">{errors[`mucogingivalDeformities.recession.${rowIndex}.${field}`]}</p>
                      )}
                    </td>
                  ))}
                  <td className="border px-2 py-1 text-center">
                    <button
                      onClick={() => handleRemoveRecessionRow('mucogingivalDeformities', 'recession', rowIndex)}
                      className="text-[#0077B6] hover:text-[#005f92] transition-all"
                    >
                      Remove
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        <button
          onClick={() => handleAddRecessionRow('mucogingivalDeformities', 'recession')}
          className="mt-4 px-4 py-2 bg-[#20B2AA] text-white rounded-md hover:bg-[#1a9690] transition-all shadow-sm"
        >
          Add Recession Row
        </button>
      </div>
    </motion.div>
  );

  const renderRadiographicExamination = () => (
    <motion.div variants={item} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {Object.keys(formData.radiographicExamination).map((field) => (
          <div key={field}>
            <label className="block text-base font-medium text-gray-600 capitalize">{field.replace(/([A-Z])/g, ' $1')}</label>
            <input
              type="text"
              value={formData.radiographicExamination[field] || ''}
              onChange={(e) => handleChange('radiographicExamination', field, e.target.value)}
              className="mt-1 block w-full border border-gray-300 rounded-md p-2 focus:ring-blue-500 focus:border-blue-500"
            />
            {errors[`radiographicExamination.${field}`] && (
              <p className="text-red-500 text-sm mt-1">{errors[`radiographicExamination.${field}`]}</p>
            )}
          </div>
        ))}
      </div>
      <div>
        <h3 className="text-lg font-semibold text-gray-700 mb-2">Upload X-Ray</h3>
        <input
          type="file"
          accept="image/*"
          onChange={handleXrayUpload}
          className="mt-1 block w-full border border-gray-300 rounded-md p-2 focus:ring-blue-500 focus:border-blue-500"
        />
        {xrayFile && (
          <div className="mt-4">
            <img src={URL.createObjectURL(xrayFile)} alt="X-Ray" className="max-w-full h-auto rounded-md" />
          </div>
        )}
      </div>
    </motion.div>
  );

  const renderDiagnosisAndTreatment = () => (
    <motion.div variants={item} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-base font-medium text-gray-600">Gingival Diseases</label>
          <input
            type="text"
            value={formData.diagnosisAndTreatment.gingivalDiseases || ''}
            onChange={(e) => handleChange('diagnosisAndTreatment', 'gingivalDiseases', e.target.value)}
            className="mt-1 block w-full border border-gray-300 rounded-md p-2 focus:ring-blue-500 focus:border-blue-500"
          />
          {errors['diagnosisAndTreatment.gingivalDiseases'] && (
            <p className="text-red-500 text-sm mt-1">{errors['diagnosisAndTreatment.gingivalDiseases']}</p>
          )}
        </div>
        <div>
          <label className="block text-base font-medium text-gray-600">Final Diagnosis</label>
          <input
            type="text"
            value={formData.diagnosisAndTreatment.finalDiagnosis || ''}
            onChange={(e) => handleChange('diagnosisAndTreatment', 'finalDiagnosis', e.target.value)}
            className="mt-1 block w-full border border-gray-300 rounded-md p-2 focus:ring-blue-500 focus:border-blue-500"
          />
          {errors['diagnosisAndTreatment.finalDiagnosis'] && (
            <p className="text-red-500 text-sm mt-1">{errors['diagnosisAndTreatment.finalDiagnosis']}</p>
          )}
        </div>
        <div>
          <label className="block text-base font-medium text-gray-600">Other Diagnosis</label>
          <input
            type="text"
            value={formData.diagnosisAndTreatment.otherDiagnosis || ''}
            onChange={(e) => handleChange('diagnosisAndTreatment', 'otherDiagnosis', e.target.value)}
            className="mt-1 block w-full border border-gray-300 rounded-md p-2 focus:ring-blue-500 focus:border-blue-500"
          />
          {errors['diagnosisAndTreatment.otherDiagnosis'] && (
            <p className="text-red-500 text-sm mt-1">{errors['diagnosisAndTreatment.otherDiagnosis']}</p>
          )}
        </div>
      </div>
      <div>
        <h3 className="text-lg font-semibold text-gray-700 mb-2">Periodontal Diseases</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full border-collapse">
            <thead>
              <tr>
                {Object.keys(formData.diagnosisAndTreatment.periodontalDiseases[0]).map((key) => (
                  <th key={key} className="border px-2 py-1 text-sm capitalize">{key.replace(/([A-Z])/g, ' $1')}</th>
                ))}
                <th className="border px-2 py-1 text-sm">Actions</th>
              </tr>
            </thead>
            <tbody>
              {formData.diagnosisAndTreatment.periodontalDiseases.map((row, rowIndex) => (
                <tr key={rowIndex}>
                  {Object.keys(row).map((field) => (
                    <td key={field} className="border px-2 py-1">
                      <input
                        type="text"
                        value={row[field] || ''}
                        onChange={(e) => handlePeriodontalDiseaseRowChange('diagnosisAndTreatment', 'periodontalDiseases', rowIndex, field, e.target.value)}
                        className="w-full border border-gray-300 rounded-md p-1 focus:ring-blue-500 focus:border-blue-500"
                      />
                      {errors[`diagnosisAndTreatment.periodontalDiseases.${rowIndex}.${field}`] && (
                        <p className="text-red-500 text-xs mt-1">{errors[`diagnosisAndTreatment.periodontalDiseases.${rowIndex}.${field}`]}</p>
                      )}
                    </td>
                  ))}
                  <td className="border px-2 py-1 text-center">
                    <button
                      onClick={() => handleRemovePeriodontalDiseaseRow('diagnosisAndTreatment', 'periodontalDiseases', rowIndex)}
                      className="text-[#0077B6] hover:text-[#005f92] transition-all"
                    >
                      Remove
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        <button
          onClick={() => handleAddPeriodontalDiseaseRow('diagnosisAndTreatment', 'periodontalDiseases')}
          className="mt-4 px-4 py-2 bg-[#20B2AA] text-white rounded-md hover:bg-[#1a9690] transition-all shadow-sm"
        >
          Add Periodontal Disease Row
        </button>
      </div>
      <div>
        <h3 className="text-lg font-semibold text-gray-700 mb-2">Factors</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Object.keys(formData.diagnosisAndTreatment.factors).map((field) => (
            <div key={field}>
              <label className="block text-base font-medium text-gray-600 capitalize">{field}</label>
              <input
                type="text"
                value={formData.diagnosisAndTreatment.factors[field] || ''}
                onChange={(e) => {
                  const updatedFactors = {
                    ...formData.diagnosisAndTreatment.factors,
                    [field]: e.target.value
                  };
                  handleNestedChange('diagnosisAndTreatment', 'factors', updatedFactors);
                }}
                className="mt-1 block w-full border border-gray-300 rounded-md p-2 focus:ring-blue-500 focus:border-blue-500"
              />
              {errors[`diagnosisAndTreatment.factors.${field}`] && (
                <p className="text-red-500 text-sm mt-1">{errors[`diagnosisAndTreatment.factors.${field}`]}</p>
              )}
            </div>
          ))}
        </div>
      </div>
      <div>
        <h3 className="text-lg font-semibold text-gray-700 mb-2">Treatment Plan</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-base font-medium text-gray-600">Emergency Phase</label>
            <textarea
              value={formData.diagnosisAndTreatment.treatmentPlan.emergencyPhase || ''}
              onChange={(e) => {
                const updatedTreatmentPlan = {
                  ...formData.diagnosisAndTreatment.treatmentPlan,
                  emergencyPhase: e.target.value
                };
                handleNestedChange('diagnosisAndTreatment', 'treatmentPlan', updatedTreatmentPlan);
              }}
              className="mt-1 block w-full border border-gray-300 rounded-md p-2 focus:ring-blue-500 focus:border-blue-500"
              rows="3"
            />
            {errors['diagnosisAndTreatment.treatmentPlan.emergencyPhase'] && (
              <p className="text-red-500 text-sm mt-1">{errors['diagnosisAndTreatment.treatmentPlan.emergencyPhase']}</p>
            )}
          </div>
          <div>
            <h4 className="text-md font-semibold text-gray-700 mb-2">Etiotropic Phase</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Object.keys(formData.diagnosisAndTreatment.treatmentPlan.etiotropicPhase)
                .filter((key) => !key.includes('Site'))
                .map((field) => (
                  <div key={field} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={formData.diagnosisAndTreatment.treatmentPlan.etiotropicPhase[field]}
                      onChange={(e) => {
                        const updatedEtiotropicPhase = {
                          ...formData.diagnosisAndTreatment.treatmentPlan.etiotropicPhase,
                          [field]: e.target.checked
                        };
                        const updatedTreatmentPlan = {
                          ...formData.diagnosisAndTreatment.treatmentPlan,
                          etiotropicPhase: updatedEtiotropicPhase
                        };
                        handleNestedChange('diagnosisAndTreatment', 'treatmentPlan', updatedTreatmentPlan);
                      }}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label className="text-sm font-medium text-gray-600 capitalize">{field.replace(/([A-Z])/g, ' $1')}</label>
                    {formData.diagnosisAndTreatment.treatmentPlan.etiotropicPhase[field] && (
                      <input
                        type="text"
                        value={formData.diagnosisAndTreatment.treatmentPlan.etiotropicPhase[`${field}Site`] || ''}
                        onChange={(e) => {
                          const updatedEtiotropicPhase = {
                            ...formData.diagnosisAndTreatment.treatmentPlan.etiotropicPhase,
                            [`${field}Site`]: e.target.value
                          };
                          const updatedTreatmentPlan = {
                            ...formData.diagnosisAndTreatment.treatmentPlan,
                            etiotropicPhase: updatedEtiotropicPhase
                          };
                          handleNestedChange('diagnosisAndTreatment', 'treatmentPlan', updatedTreatmentPlan);
                        }}
                        placeholder="Specify site..."
                        className="mt-1 block w-full border border-gray-300 rounded-md p-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    )}
                  </div>
                ))}
            </div>
          </div>
          {['surgicalPhase', 'restorativePhase', 'maintenancePhase', 'consultation', 'recallMaintenance'].map((field) => (
            <div key={field}>
              <label className="block text-sm font-medium text-gray-600 capitalize">{field.replace(/([A-Z])/g, ' $1')}</label>
              <textarea
                value={formData.diagnosisAndTreatment.treatmentPlan[field] || ''}
                onChange={(e) => {
                  const updatedTreatmentPlan = {
                    ...formData.diagnosisAndTreatment.treatmentPlan,
                    [field]: e.target.value
                  };
                  handleNestedChange('diagnosisAndTreatment', 'treatmentPlan', updatedTreatmentPlan);
                }}
                className="mt-1 block w-full border border-gray-300 rounded-md p-2 focus:ring-blue-500 focus:border-blue-500"
                rows="3"
              />
              {errors[`diagnosisAndTreatment.treatmentPlan.${field}`] && (
                <p className="text-red-500 text-sm mt-1">{errors[`diagnosisAndTreatment.treatmentPlan.${field}`]}</p>
              )}
            </div>
          ))}
        </div>
      </div>
    </motion.div>
  );

  const renderCharting = () => (
    <motion.div variants={item} className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-700 mb-2">Upper Arch</h3>
        <div className="space-y-4">
          <div>
            <h4 className="text-md font-semibold text-gray-600 capitalize">Labial View</h4>
            <div className="overflow-x-auto">
              <table className="min-w-full border-collapse">
                <thead>
                  <tr>
                    <th className="border px-2 py-1 text-sm">Tooth</th>
                    {upperTeethNumbers.map((number) => (
                      <th key={number} className="border px-2 py-1 text-sm">{number}</th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {['M', 'F'].map((field) => (
                    <tr key={field}>
                      <td className="border px-2 py-1 text-sm font-medium">{field}</td>
                      {formData.charting.upper.labial[field].map((value, index) => (
                        <td key={index} className="border px-2 py-1">
                          <input
                            type="text"
                            value={value || ''}
                            onChange={(e) => handleChartingChange('upper', 'labial', field, index, 0, e.target.value)}
                            className="w-12 text-center border border-gray-300 rounded-md p-1 focus:ring-blue-500 focus:border-blue-500"
                          />
                        </td>
                      ))}
                    </tr>
                  ))}
                  {['CAL', 'PD'].map((field) => (
                    <tr key={field}>
                      <td className="border px-2 py-1 text-sm font-medium">{field}</td>
                      {formData.charting.upper.labial[field].map((values, index) => (
                        <td key={index} className="border px-2 py-1">
                          <div className="grid grid-cols-3 gap-1">
                            {values.map((value, subIndex) => (
                              <input
                                key={subIndex}
                                type="text"
                                value={value || ''}
                                onChange={(e) => handleChartingChange('upper', 'labial', field, index, subIndex, e.target.value)}
                                className="w-full text-center border border-gray-300 rounded-md p-1 focus:ring-blue-500 focus:border-blue-500"
                                style={{ height: '20px' }}
                              />
                            ))}
                          </div>
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
          <div className="flex justify-center space-x-2 my-4">
            {upperTeethNumbers.map((number) => (
              <img
                key={number}
                src={`${process.env.PUBLIC_URL}/imgs/teeth/${number}.png`}
                alt={`Tooth ${number}`}
                className="w-16 h-16 object-contain"
              />
            ))}
          </div>
          <div>
            <h4 className="text-md font-semibold text-gray-600 capitalize">Palatal View</h4>
            <div className="overflow-x-auto">
              <table className="min-w-full border-collapse">
                <thead>
                  <tr>
                    <th className="border px-2 py-1 text-sm">Tooth</th>
                    {upperTeethNumbers.map((number) => (
                      <th key={number} className="border px-2 py-1 text-sm">{number}</th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {['CAL', 'PD'].map((field) => (
                    <tr key={field}>
                      <td className="border px-2 py-1 text-sm font-medium">{field}</td>
                      {formData.charting.upper.palatal[field].map((values, index) => (
                        <td key={index} className="border px-2 py-1">
                          <div className="grid grid-cols-3 gap-1">
                            {values.map((value, subIndex) => (
                              <input
                                key={subIndex}
                                type="text"
                                value={value || ''}
                                onChange={(e) => handleChartingChange('upper', 'palatal', field, index, subIndex, e.target.value)}
                                className="w-full text-center border border-gray-300 rounded-md p-1 focus:ring-blue-500 focus:border-blue-500"
                                style={{ height: '20px' }}
                              />
                            ))}
                          </div>
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
      <div>
        <h3 className="text-lg font-semibold text-gray-700 mb-2">Lower Arch</h3>
        <div className="space-y-4">
          <div>
            <h4 className="text-md font-semibold text-gray-600 capitalize">Buccal View</h4>
            <div className="overflow-x-auto">
              <table className="min-w-full border-collapse">
                <thead>
                  <tr>
                    <th className="border px-2 py-1 text-sm">Tooth</th>
                    {lowerTeethNumbers.map((number) => (
                      <th key={number} className="border px-2 py-1 text-sm">{number}</th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {['CAL', 'PD'].map((field) => (
                    <tr key={field}>
                      <td className="border px-2 py-1 text-sm font-medium">{field}</td>
                      {formData.charting.lower.buccal[field].map((values, index) => (
                        <td key={index} className="border px-2 py-1">
                          <div className="grid grid-cols-3 gap-1">
                            {values.map((value, subIndex) => (
                              <input
                                key={subIndex}
                                type="text"
                                value={value || ''}
                                onChange={(e) => handleChartingChange('lower', 'buccal', field, index, subIndex, e.target.value)}
                                className="w-full text-center border border-gray-300 rounded-md p-1 focus:ring-blue-500 focus:border-blue-500"
                                style={{ height: '20px' }}
                              />
                            ))}
                          </div>
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
          <div className="flex justify-center space-x-2 my-4">
            {lowerTeethNumbers.map((number) => (
              <img
                key={number}
                src={`${process.env.PUBLIC_URL}/imgs/teeth/${number}.png`}
                alt={`Tooth ${number}`}
                className="w-16 h-16 object-contain"
              />
            ))}
          </div>
          <div>
            <h4 className="text-md font-semibold text-gray-600 capitalize">Lingual View</h4>
            <div className="overflow-x-auto">
              <table className="min-w-full border-collapse">
                <thead>
                  <tr>
                    <th className="border px-2 py-1 text-sm">Tooth</th>
                    {lowerTeethNumbers.map((number) => (
                      <th key={number} className="border px-2 py-1 text-sm">{number}</th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {['PD', 'CAL', 'F', 'M'].map((field) => (
                    <tr key={field}>
                      <td className="border px-2 py-1 text-sm font-medium">{field}</td>
                      {field === 'F' || field === 'M' ? (
                        // For F and M rows
                        formData.charting.lower.lingual[field].map((value, index) => (
                          <td key={index} className="border px-2 py-1">
                            <input
                              type="text"
                              value={value || ''}
                              onChange={(e) => handleChartingChange('lower', 'lingual', field, index, 0, e.target.value)}
                              className="w-12 text-center border border-gray-300 rounded-md p-1 focus:ring-blue-500 focus:border-blue-500"
                            />
                          </td>
                        ))
                      ) : (
                        // For CAL and PD rows
                        formData.charting.lower.lingual[field].map((values, index) => (
                          <td key={index} className="border px-2 py-1">
                            <div className="grid grid-cols-3 gap-1">
                              {values.map((value, subIndex) => (
                                <input
                                  key={subIndex}
                                  type="text"
                                  value={value || ''}
                                  onChange={(e) => handleChartingChange('lower', 'lingual', field, index, subIndex, e.target.value)}
                                  className="w-full text-center border border-gray-300 rounded-md p-1 focus:ring-blue-500 focus:border-blue-500"
                                  style={{ height: '20px' }}
                                />
                              ))}
                            </div>
                          </td>
                        ))
                      )}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 }
  };

  const tabs = [
    { id: 'medical', label: 'Medical' },
    { id: 'generalInformation', label: 'General Information' },
    { id: 'intraOralExamination', label: 'Intra-Oral Examination' },
    { id: 'mucogingivalDeformities', label: 'Mucogingival Deformities' },
    { id: 'radiographicExamination', label: 'Radiographic Examination' },
    { id: 'diagnosisAndTreatment', label: 'Diagnosis & Treatment' },
    { id: 'charting', label: 'Charting' }
  ];

  return (
    <div className="w-full mx-auto p-6 bg-white rounded-xl shadow-md border border-[rgba(0,119,182,0.1)]">
      <h1 className="text-2xl font-bold text-[#0077B6] mb-6">Periodontics Sheet</h1>
      <div className="border-b border-[rgba(0,119,182,0.1)] mb-6">
        <nav className="-mb-px flex space-x-4 overflow-x-auto">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`${
                activeTab === tab.id
                  ? 'border-b-2 border-[#0077B6] text-[#0077B6] bg-[rgba(0,119,182,0.05)]'
                  : 'border-transparent text-gray-500 hover:text-[#0077B6] hover:bg-[rgba(0,119,182,0.03)]'
              } whitespace-nowrap py-4 px-4 border-b-2 font-medium text-sm transition-all`}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>
      <motion.div variants={container} initial="hidden" animate="show">
        {activeTab === 'medical' && (
          <div>
            <MedicalTab onSave={(medicalInfo) => {
              console.log('Medical info updated:', medicalInfo);
              // You can update the form data here if needed
            }} />
          </div>
        )}
        {activeTab === 'generalInformation' && renderGeneralInformation()}
        {activeTab === 'intraOralExamination' && renderIntraOralExamination()}
        {activeTab === 'mucogingivalDeformities' && renderMucogingivalDeformities()}
        {activeTab === 'radiographicExamination' && renderRadiographicExamination()}
        {activeTab === 'diagnosisAndTreatment' && renderDiagnosisAndTreatment()}
        {activeTab === 'charting' && renderCharting()}
      </motion.div>
      <div className="mt-8 flex justify-end gap-4">
        <button
          onClick={handleDownloadPDF}
          className="px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white font-medium rounded-lg hover:shadow-lg transition-all duration-300 flex items-center"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 mr-2"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
          Download PDF
        </button>
        <button
          onClick={handleSubmit}
          className="px-6 py-2 bg-[#0077B6] text-white font-medium rounded-lg hover:bg-[#005f92] transition-all shadow-sm"
        >
          Save Sheet
        </button>
      </div>
    </div>
  );
};

export default PeriodonticsSheet;
