# Railway Production Deployment Fix

## 🚨 Issues Identified

1. **Port Binding** - Server not listening on all interfaces
2. **Environment Variables** - Running in development mode
3. **CORS Configuration** - May need updates for production
4. **Health Check** - Need to ensure proper response

## ✅ Fixes Applied

### 1. Fixed Server Binding

**Updated `server.js`:**
```javascript
const serverInstance = server.listen(PORT, '0.0.0.0', () => {
  console.log(`✅ Server running on port ${PORT}`);
  console.log(`🌐 Server URL: http://0.0.0.0:${PORT}`);
});
```

**Why this fixes it:**
- `0.0.0.0` binds to all network interfaces
- Railway can now reach the application
- Proper for containerized deployments

### 2. Environment Variables Required

Make sure these are set in Railway Variables:

```env
# Server Configuration
PORT=8080
NODE_ENV=production

# Database Configuration
MONGO_URI=mongodb+srv://username:<EMAIL>/dentlyzer

# JWT Configuration
JWT_SECRET=your_production_jwt_secret_here
JWT_REFRESH_SECRET=your_production_refresh_secret_here
JWT_ACCESS_EXPIRATION=15m
JWT_REFRESH_EXPIRATION=7d

# Frontend URL (update with your actual Vercel URL)
FRONTEND_URL=https://your-vercel-app.vercel.app

# File Upload Configuration
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=50mb
```

### 3. CORS Updates for Production

The CORS configuration should work, but if you need to add your Railway domain:

```javascript
const allowedOrigins = [
  'http://localhost:3000',
  'https://odenta.vercel.app',
  'https://Odenta.vercel.app',
  'https://odenta.vercel.app/',
  'https://Odenta.vercel.app/',
  'https://your-railway-app.railway.app'  // Add your Railway domain
];
```

## 🔧 Troubleshooting Steps

### Step 1: Check Environment Variables
1. Go to Railway Dashboard
2. Select your project
3. Go to Variables tab
4. Ensure all required variables are set

### Step 2: Test Health Endpoint
After deployment, test:
- `https://your-app.railway.app/api/health`
- Should return: `{"status":"OK","timestamp":"..."}`

### Step 3: Check Logs
1. Go to Railway Dashboard
2. Select your project
3. Go to Deployments tab
4. Click on latest deployment
5. Check logs for any errors

### Step 4: Test Root Endpoint
- `https://your-app.railway.app/`
- Should return: `Welcome to the ODenta API!`

## 🎯 Common Issues and Solutions

### Issue 1: "Application failed to respond"
**Cause:** Server not binding to correct interface
**Solution:** ✅ Fixed with `0.0.0.0` binding

### Issue 2: Environment variables not set
**Cause:** Missing required environment variables
**Solution:** Set all variables in Railway dashboard

### Issue 3: CORS errors
**Cause:** Frontend URL not in allowed origins
**Solution:** Update CORS configuration with Railway domain

### Issue 4: Database connection issues
**Cause:** MONGO_URI not set or incorrect
**Solution:** Verify MongoDB connection string

## 📋 Verification Checklist

After deployment, verify:

- ✅ Server starts without errors
- ✅ Health endpoint responds: `/api/health`
- ✅ Root endpoint responds: `/`
- ✅ Environment shows: `production`
- ✅ Port shows: `8080` (or Railway's assigned port)
- ✅ Frontend URL is correct
- ✅ Database connection successful

## 🚀 Next Steps

1. **Commit the server.js fix:**
   ```bash
   git add server.js
   git commit -m "Fix Railway deployment: bind to 0.0.0.0"
   git push origin main
   ```

2. **Update Railway environment variables** with your actual values

3. **Test the deployment** using the health endpoint

4. **Update frontend** to use the new Railway URL

The server binding fix should resolve the "Application failed to respond" error. 