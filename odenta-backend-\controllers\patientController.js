const Joi = require('joi');
const { FirestoreHelpers } = require('../config/firebaseDb');
const { COLLECTIONS } = require('../models/firebase/index');
const { PatientHelpers, createPatientSchema } = require('../models/firebase/Patient');
const { upload, isCloudinaryConfigured } = require('../middleware/upload');

// Import Cloudinary helpers if configured
let deleteFromCloudinary, extractPublicId;
if (isCloudinaryConfigured()) {
  const cloudinaryHelpers = require('../config/cloudinary');
  deleteFromCloudinary = cloudinaryHelpers.deleteFromCloudinary;
  extractPublicId = cloudinaryHelpers.extractPublicId;
}

// Helper function to get file URL based on storage type
const getFileUrl = (file) => {
  if (isCloudinaryConfigured()) {
    // For Cloudinary, the file.path contains the full URL
    return file.path;
  } else {
    // For local storage, create relative path
    let relativePath = file.path.replace(/\\/g, '/');
    if (!relativePath.startsWith('uploads/')) {
      relativePath = 'uploads/' + relativePath.split('/').pop();
    }
    return relativePath;
  }
};

// Sheet validation schema
const sheetSchema = Joi.object({
  type: Joi.string()
    .valid('Operative', 'Fixed Prosthodontics', 'Removable Prosthodontics', 'Endodontics', 'Periodontics')
    .required(),
  diagnosis: Joi.string().required(),
  treatmentPlan: Joi.string().required(),
  notes: Joi.string().allow('').default(''),
  specificData: Joi.object().default({})
});

// Patient validation schema for creation
const patientSchema = Joi.object({
  nationalId: Joi.string().required(),
  drId: Joi.string().allow('unassigned', 'N/A', '').default('unassigned'),
  fullName: Joi.string().required(),
  age: Joi.number().min(0).default(18),
  phoneNumber: Joi.string().allow('').default(''),
  gender: Joi.string().valid('male', 'female', 'other').default('other'),
  address: Joi.string().allow('').default(''),
  occupation: Joi.string().allow('').default(''),
  medicalInfo: Joi.object({
    chronicDiseases: Joi.array().items(Joi.string()).default([]),
    recentSurgicalProcedures: Joi.string().allow('').default(''),
    currentMedications: Joi.string().allow('').default(''),
    chiefComplaint: Joi.string().allow('').default(''),
  }).default({ chiefComplaint: '' }),
  xrays: Joi.array().default([]),
  galleryImages: Joi.array().default([])
});

// Update patient validation schema (all fields optional)
const updatePatientSchema = Joi.object({
  nationalId: Joi.string().optional(),
  drId: Joi.string().optional(),
  fullName: Joi.string().optional(),
  age: Joi.number().optional(),
  phoneNumber: Joi.string().optional(),
  gender: Joi.string().valid('male', 'female', 'other').optional(),
  address: Joi.string().allow('').optional(),
  occupation: Joi.string().allow('').optional(),
  medicalInfo: Joi.object({
    chronicDiseases: Joi.array().items(Joi.string()).default([]),
    recentSurgicalProcedures: Joi.string().allow('').default(''),
    currentMedications: Joi.string().allow('').default(''),
    chiefComplaint: Joi.string().allow('').default(''),
  }).optional(),
  xrays: Joi.array().optional(),
  galleryImages: Joi.array().optional()
});

// Create patient
const createPatient = async (req, res) => {
  console.log('Creating patient with data:', req.body);

  // Check if this is an assistant creating a patient for a student
  const isAssistant = req.user && req.user.role === 'assistant';

  const { error, value } = patientSchema.validate(req.body, {
    abortEarly: false,
    allowUnknown: true
  });

  if (error) {
    console.error('Validation error:', error.details);
    return res.status(400).json({
      message: 'Validation error',
      errors: error.details.map(d => d.message)
    });
  }

  try {
    // Allow multiple patients with the same nationalId as long as drId is different
    // Skip duplicate check if patient is unassigned
    if (value.drId && value.drId !== 'unassigned' && value.drId !== 'N/A' && value.drId !== '') {
      const existingPatients = await FirestoreHelpers.getByField(
        COLLECTIONS.PATIENTS,
        'nationalId',
        value.nationalId
      );
      const sameStudentPatient = existingPatients && existingPatients.find(p => p.drId === value.drId);

      if (sameStudentPatient) {
        // Patient with this nationalId is already assigned to this student
        return res.status(200).json({
          message: 'Patient already exists for this student',
          patient: sameStudentPatient
        });
      }
    }

    const cleanedMedicalInfo = {
      chiefComplaint: value.medicalInfo.chiefComplaint,
      ...(value.medicalInfo.chronicDiseases?.length > 0 && {
        chronicDiseases: value.medicalInfo.chronicDiseases
      }),
      ...(value.medicalInfo.recentSurgicalProcedures && {
        recentSurgicalProcedures: value.medicalInfo.recentSurgicalProcedures
      }),
      ...(value.medicalInfo.currentMedications && {
        currentMedications: value.medicalInfo.currentMedications
      })
    };

    const patientData = {
      nationalId: value.nationalId,
      drId: value.drId,
      fullName: value.fullName,
      age: value.age,
      phoneNumber: value.phoneNumber,
      gender: value.gender,
      address: value.address,
      occupation: value.occupation,
      medicalInfo: cleanedMedicalInfo,
      xrays: req.files?.xrays?.map(file => ({
        url: getFileUrl(file),
        date: new Date(),
        note: ''
      })) || [],
      galleryImages: req.files?.galleryImages?.map(file => ({
        url: getFileUrl(file),
        date: new Date(),
        note: ''
      })) || [],
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const patient = await FirestoreHelpers.create(COLLECTIONS.PATIENTS, patientData);

    res.status(201).json({
      message: 'Patient created successfully',
      patient: patient
    });
  } catch (error) {
    console.error('Error creating patient:', error);
    if (value && value.nationalId) {
      console.error('National ID attempted:', value.nationalId);
    }
    // Handle Firebase duplicate error for nationalId
    if (error.message && error.message.includes('nationalId')) {
      return res.status(400).json({
        message: 'A patient with this National ID already exists. Please use a different National ID.'
      });
    }

    res.status(500).json({
      message: 'Server error while creating patient',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get patient by ID
const getPatientById = async (req, res) => {
  try {
    console.log('getPatientById called with nationalId:', req.params.nationalId);
    const patient = await FirestoreHelpers.findOne(
      COLLECTIONS.PATIENTS,
      { field: 'nationalId', operator: '==', value: req.params.nationalId }
    );

    console.log('Found patient in getPatientById:', patient);

    if (!patient) {
      console.log('Patient not found in getPatientById with nationalId:', req.params.nationalId);
      return res.status(404).json({ message: 'Patient not found' });
    }

    patient.medicalInfo = patient.medicalInfo || {
      chronicDiseases: [],
      recentSurgicalProcedures: '',
      currentMedications: '',
      chiefComplaint: ''
    };
    patient.treatmentSheets = patient.treatmentSheets || [];

    res.json(patient);
  } catch (error) {
    console.error('Error fetching patient:', error);
    res.status(500).json({
      message: 'Server error while fetching patient',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Update patient
const updatePatient = async (req, res) => {
  console.log('updatePatient called with params:', req.params);
  console.log('updatePatient called with body:', req.body);
  
  const { error, value } = updatePatientSchema.validate(req.body, {
    abortEarly: false,
    allowUnknown: true
  });

  if (error) {
    console.log('Validation error:', error.details);
    return res.status(400).json({
      message: 'Validation error',
      errors: error.details.map(d => d.message)
    });
  }

  try {
    console.log('Looking for patient with nationalId:', req.params.nationalId);
    // First find the patient
    const existingPatient = await FirestoreHelpers.findOne(
      COLLECTIONS.PATIENTS,
      { field: 'nationalId', operator: '==', value: req.params.nationalId }
    );

    console.log('Found patient:', existingPatient);

    if (!existingPatient) {
      console.log('Patient not found with nationalId:', req.params.nationalId);
      console.log('Attempting to create patient...');
      
      // Try to create the patient if it doesn't exist
      try {
        const patientData = {
          nationalId: req.params.nationalId,
          drId: value.drId,
          fullName: value.fullName || 'Unknown',
          age: value.age || 0,
          phoneNumber: value.phoneNumber || '',
          gender: value.gender || 'other',
          address: value.address || '',
          occupation: value.occupation || '',
          medicalInfo: {
            chiefComplaint: value.medicalInfo?.chiefComplaint || '',
            chronicDiseases: value.medicalInfo?.chronicDiseases || [],
            recentSurgicalProcedures: value.medicalInfo?.recentSurgicalProcedures || '',
            currentMedications: value.medicalInfo?.currentMedications || ''
          },
          createdAt: new Date(),
          updatedAt: new Date()
        };
        
        const newPatient = await FirestoreHelpers.create(COLLECTIONS.PATIENTS, patientData);
        console.log('Created new patient:', newPatient);
        
        return res.json({
          message: 'Patient created and updated successfully',
          patient: newPatient
        });
      } catch (createError) {
        console.error('Error creating patient:', createError);
        return res.status(500).json({ 
          message: 'Patient not found and could not be created',
          error: process.env.NODE_ENV === 'development' ? createError.message : undefined
        });
      }
    }

    const updateData = {
      ...value,
      medicalInfo: {
        chiefComplaint: value.medicalInfo?.chiefComplaint || existingPatient.medicalInfo?.chiefComplaint || '',
        chronicDiseases: value.medicalInfo?.chronicDiseases || existingPatient.medicalInfo?.chronicDiseases || [],
        recentSurgicalProcedures: value.medicalInfo?.recentSurgicalProcedures || existingPatient.medicalInfo?.recentSurgicalProcedures || '',
        currentMedications: value.medicalInfo?.currentMedications || existingPatient.medicalInfo?.currentMedications || ''
      },
      updatedAt: new Date()
    };

    const patient = await FirestoreHelpers.update(
      COLLECTIONS.PATIENTS,
      existingPatient.id,
      updateData
    );

    res.json({
      message: 'Patient updated successfully',
      patient
    });
  } catch (error) {
    console.error('Error updating patient:', error);
    res.status(500).json({
      message: 'Server error while updating patient',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Add treatment sheet
const addTreatmentSheet = async (req, res) => {
  const { error, value } = sheetSchema.validate(req.body, { abortEarly: false });

  if (error) {
    return res.status(400).json({
      message: 'Validation error',
      errors: error.details.map(d => d.message)
    });
  }

  try {
    const patient = await FirestoreHelpers.findOne(
      COLLECTIONS.PATIENTS,
      { field: 'nationalId', operator: '==', value: req.params.nationalId }
    );
    if (!patient) {
      return res.status(404).json({ message: 'Patient not found' });
    }

    const newSheet = {
      type: value.type,
      details: {
        diagnosis: value.diagnosis,
        treatmentPlan: value.treatmentPlan,
        notes: value.notes,
        specificData: value.specificData
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Initialize treatmentSheets array if it doesn't exist
    if (!patient.treatmentSheets) {
      patient.treatmentSheets = [];
    }

    patient.treatmentSheets.push(newSheet);

    await FirestoreHelpers.update(
      COLLECTIONS.PATIENTS,
      patient.id,
      { 
        treatmentSheets: patient.treatmentSheets,
        updatedAt: new Date()
      }
    );

    res.status(201).json({
      message: 'Treatment sheet added successfully',
      sheet: {
        ...newSheet,
        _id: newSheet.createdAt.getTime().toString() // Generate a unique ID for the frontend
      }
    });
  } catch (error) {
    console.error('Error adding treatment sheet:', error);
    res.status(500).json({
      message: 'Server error while adding treatment sheet',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get all treatment sheets
const getTreatmentSheets = async (req, res) => {
  try {
    const patient = await FirestoreHelpers.findOne(
      COLLECTIONS.PATIENTS,
      { field: 'nationalId', operator: '==', value: req.params.nationalId }
    );

    if (!patient) {
      return res.status(404).json({ message: 'Patient not found' });
    }

    res.json(patient.treatmentSheets || []);
  } catch (error) {
    console.error('Error fetching treatment sheets:', error);
    res.status(500).json({
      message: 'Server error while fetching treatment sheets',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Update treatment sheet
const updateTreatmentSheet = async (req, res) => {
  const { error, value } = sheetSchema.validate(req.body, { abortEarly: false });

  if (error) {
    return res.status(400).json({
      message: 'Validation error',
      errors: error.details.map(d => d.message)
    });
  }

  try {
    const patient = await FirestoreHelpers.findOne(
      COLLECTIONS.PATIENTS,
      { field: 'nationalId', operator: '==', value: req.params.nationalId }
    );
    if (!patient) {
      return res.status(404).json({ message: 'Patient not found' });
    }

    const sheet = patient.treatmentSheets.find(s => s._id.toString() === req.params.sheetId);
    if (!sheet) {
      return res.status(404).json({ message: 'Treatment sheet not found' });
    }

    sheet.type = value.type;
    sheet.details = {
      diagnosis: value.diagnosis,
      treatmentPlan: value.treatmentPlan,
      notes: value.notes,
      specificData: value.specificData
    };
    sheet.updatedAt = new Date();

    await FirestoreHelpers.update(
      COLLECTIONS.PATIENTS,
      patient.id,
      { treatmentSheets: patient.treatmentSheets }
    );

    res.json({
      message: 'Treatment sheet updated successfully',
      sheet
    });
  } catch (error) {
    console.error('Error updating treatment sheet:', error);
    res.status(500).json({
      message: 'Server error while updating treatment sheet',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Delete treatment sheet
const deleteTreatmentSheet = async (req, res) => {
  try {
    const patient = await FirestoreHelpers.findOne(
      COLLECTIONS.PATIENTS,
      { field: 'nationalId', operator: '==', value: req.params.nationalId }
    );
    if (!patient) {
      return res.status(404).json({ message: 'Patient not found' });
    }

    const sheetIndex = patient.treatmentSheets.findIndex(s => s._id.toString() === req.params.sheetId);
    if (sheetIndex === -1) {
      return res.status(404).json({ message: 'Treatment sheet not found' });
    }

    patient.treatmentSheets.splice(sheetIndex, 1);
    await FirestoreHelpers.update(
      COLLECTIONS.PATIENTS,
      patient.id,
      { treatmentSheets: patient.treatmentSheets }
    );

    res.json({ message: 'Treatment sheet deleted successfully' });
  } catch (error) {
    console.error('Error deleting treatment sheet:', error);
    res.status(500).json({
      message: 'Server error while deleting treatment sheet',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Add X-ray
const addXray = async (req, res) => {
  try {
    console.log('addXray called with params:', req.params);
    console.log('addXray files:', req.files);
    
    const patient = await FirestoreHelpers.findOne(
      COLLECTIONS.PATIENTS,
      { field: 'nationalId', operator: '==', value: req.params.nationalId }
    );
    if (!patient) return res.status(404).json({ message: 'Patient not found' });
    if (!req.files || !req.files.xrays) return res.status(400).json({ message: 'No X-ray files uploaded' });

    console.log('Found patient:', patient.nationalId);
    console.log('X-ray files:', req.files.xrays);

    // Create proper URL paths for the files with unique IDs
    const newXrays = req.files.xrays.map(file => ({
      _id: Date.now().toString() + Math.random().toString(36).substr(2, 9), // Generate unique ID
      url: getFileUrl(file),
      date: new Date(),
      note: ''
    }));

    console.log('New X-rays to add:', newXrays);

    patient.xrays.push(...newXrays);
    await FirestoreHelpers.update(
      COLLECTIONS.PATIENTS,
      patient.id,
      { xrays: patient.xrays }
    );

    console.log(`Added ${newXrays.length} X-rays for patient ${patient.nationalId}`);
    res.json(patient.xrays);
  } catch (error) {
    console.error('Error adding X-rays:', error);
    res.status(500).json({
      message: 'Server error while adding X-rays',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Add gallery image
const addGalleryImage = async (req, res) => {
  try {
    console.log('addGalleryImage called with params:', req.params);
    console.log('addGalleryImage files:', req.files);
    
    const patient = await FirestoreHelpers.findOne(
      COLLECTIONS.PATIENTS,
      { field: 'nationalId', operator: '==', value: req.params.nationalId }
    );
    if (!patient) return res.status(404).json({ message: 'Patient not found' });
    if (!req.files || !req.files.galleryImages) return res.status(400).json({ message: 'No gallery images uploaded' });

    console.log('Found patient:', patient.nationalId);
    console.log('Gallery files:', req.files.galleryImages);

    // Create proper URL paths for the files with unique IDs
    const newImages = req.files.galleryImages.map(file => ({
      _id: Date.now().toString() + Math.random().toString(36).substr(2, 9), // Generate unique ID
      url: getFileUrl(file),
      date: new Date(),
      note: ''
    }));

    console.log('New gallery images to add:', newImages);

    patient.galleryImages.push(...newImages);
    await FirestoreHelpers.update(
      COLLECTIONS.PATIENTS,
      patient.id,
      { galleryImages: patient.galleryImages }
    );

    console.log(`Added ${newImages.length} gallery images for patient ${patient.nationalId}`);
    res.json(patient.galleryImages);
  } catch (error) {
    console.error('Error adding gallery images:', error);
    res.status(500).json({
      message: 'Server error while adding gallery images',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Update X-ray note
const updateXrayNote = async (req, res) => {
  const { xrayId, note } = req.body;
  try {
    console.log('updateXrayNote called with:', { xrayId, note, nationalId: req.params.nationalId });
    
    const patient = await FirestoreHelpers.findOne(
      COLLECTIONS.PATIENTS,
      { field: 'nationalId', operator: '==', value: req.params.nationalId }
    );
    if (!patient) return res.status(404).json({ message: 'Patient not found' });
    
    const xray = patient.xrays.find(x => {
      const xId = x._id ? x._id.toString() : x.id;
      return xId === xrayId;
    });
    if (!xray) return res.status(404).json({ message: 'X-ray not found' });
    
    xray.note = note;
    await FirestoreHelpers.update(
      COLLECTIONS.PATIENTS,
      patient.id,
      { xrays: patient.xrays }
    );
    res.json(xray);
  } catch (error) {
    console.error('Error updating X-ray note:', error);
    res.status(500).json({ message: error.message });
  }
};

// Update gallery image note
const updateGalleryImageNote = async (req, res) => {
  const { imageId, note } = req.body;
  try {
    console.log('updateGalleryImageNote called with:', { imageId, note, nationalId: req.params.nationalId });
    
    const patient = await FirestoreHelpers.findOne(
      COLLECTIONS.PATIENTS,
      { field: 'nationalId', operator: '==', value: req.params.nationalId }
    );
    if (!patient) return res.status(404).json({ message: 'Patient not found' });
    
    const image = patient.galleryImages.find(img => {
      const imgId = img._id ? img._id.toString() : img.id;
      return imgId === imageId;
    });
    if (!image) return res.status(404).json({ message: 'Image not found' });
    
    image.note = note;
    await FirestoreHelpers.update(
      COLLECTIONS.PATIENTS,
      patient.id,
      { galleryImages: patient.galleryImages }
    );
    res.json(image);
  } catch (error) {
    console.error('Error updating gallery image note:', error);
    res.status(500).json({ message: error.message });
  }
};

// Delete X-ray
const deleteXray = async (req, res) => {
  try {
    const { nationalId, xrayId } = req.params;
    console.log('deleteXray called with params:', { nationalId, xrayId });

    const patient = await FirestoreHelpers.findOne(
      COLLECTIONS.PATIENTS,
      { field: 'nationalId', operator: '==', value: nationalId }
    );
    if (!patient) {
      console.log('Patient not found:', nationalId);
      return res.status(404).json({ message: 'Patient not found' });
    }

    console.log('Found patient:', patient.nationalId);
    console.log('Patient xrays:', patient.xrays);

    // Find the X-ray by ID - handle both string and ObjectId formats
    const xrayIndex = patient.xrays.findIndex(x => {
      const xId = x._id ? x._id.toString() : x.id;
      return xId === xrayId;
    });
    console.log('X-ray index:', xrayIndex);
    if (xrayIndex === -1) {
      console.log('X-ray not found:', xrayId);
      console.log('Available xray IDs:', patient.xrays.map(x => x._id ? x._id.toString() : x.id));
      return res.status(404).json({ message: 'X-ray not found' });
    }

    const xray = patient.xrays[xrayIndex];
    console.log('Found xray to delete:', xray);

    // Delete from Cloudinary if configured
    if (isCloudinaryConfigured() && xray.url && deleteFromCloudinary && extractPublicId) {
      try {
        const publicId = extractPublicId(xray.url);
        if (publicId) {
          await deleteFromCloudinary(publicId);
          console.log(`Deleted X-ray from Cloudinary: ${publicId}`);
        }
      } catch (cloudinaryError) {
        console.error('Error deleting X-ray from Cloudinary:', cloudinaryError);
        // Continue with database deletion even if Cloudinary deletion fails
      }
    }

    // Remove the X-ray from the array
    patient.xrays.splice(xrayIndex, 1);
    console.log('Updated xrays array:', patient.xrays);
    
    await FirestoreHelpers.update(
      COLLECTIONS.PATIENTS,
      patient.id,
      { xrays: patient.xrays }
    );

    console.log('X-ray deleted successfully');
    res.json({ message: 'X-ray deleted successfully' });
  } catch (error) {
    console.error('Error deleting X-ray:', error);
    res.status(500).json({
      message: 'Server error while deleting X-ray',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Delete gallery image
const deleteGalleryImage = async (req, res) => {
  try {
    const { nationalId, imageId } = req.params;
    console.log('deleteGalleryImage called with params:', { nationalId, imageId });

    const patient = await FirestoreHelpers.findOne(
      COLLECTIONS.PATIENTS,
      { field: 'nationalId', operator: '==', value: nationalId }
    );
    if (!patient) {
      console.log('Patient not found:', nationalId);
      return res.status(404).json({ message: 'Patient not found' });
    }

    console.log('Found patient:', patient.nationalId);
    console.log('Patient galleryImages:', patient.galleryImages);

    // Find the gallery image by ID - handle both string and ObjectId formats
    const imageIndex = patient.galleryImages.findIndex(img => {
      const imgId = img._id ? img._id.toString() : img.id;
      return imgId === imageId;
    });
    console.log('Image index:', imageIndex);
    if (imageIndex === -1) {
      console.log('Gallery image not found:', imageId);
      console.log('Available image IDs:', patient.galleryImages.map(img => img._id ? img._id.toString() : img.id));
      return res.status(404).json({ message: 'Gallery image not found' });
    }

    const image = patient.galleryImages[imageIndex];
    console.log('Found image to delete:', image);

    // Delete from Cloudinary if configured
    if (isCloudinaryConfigured() && image.url && deleteFromCloudinary && extractPublicId) {
      try {
        const publicId = extractPublicId(image.url);
        if (publicId) {
          await deleteFromCloudinary(publicId);
          console.log(`Deleted gallery image from Cloudinary: ${publicId}`);
        }
      } catch (cloudinaryError) {
        console.error('Error deleting gallery image from Cloudinary:', cloudinaryError);
        // Continue with database deletion even if Cloudinary deletion fails
      }
    }

    // Remove the image from the array
    patient.galleryImages.splice(imageIndex, 1);
    console.log('Updated galleryImages array:', patient.galleryImages);
    
    await FirestoreHelpers.update(
      COLLECTIONS.PATIENTS,
      patient.id,
      { galleryImages: patient.galleryImages }
    );

    console.log('Gallery image deleted successfully');
    res.json({ message: 'Gallery image deleted successfully' });
  } catch (error) {
    console.error('Error deleting gallery image:', error);
    res.status(500).json({
      message: 'Server error while deleting gallery image',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get patients by doctor
const getPatientsByDoctor = async (req, res) => {
  try {
    console.log('Getting patients for doctor:', req.user);

    // Use studentId or dentistId depending on the user role
    const doctorId = req.user.studentId || req.user.dentistId || req.user.id;

    console.log('Looking for patients with drId:', doctorId);
    const patients = await FirestoreHelpers.find(
      COLLECTIONS.PATIENTS,
      { field: 'drId', operator: '==', value: doctorId }
    );

    console.log(`Found ${patients.length} patients for doctor ${doctorId}`);
    res.json(patients);
  } catch (error) {
    console.error('Error getting patients by doctor:', error);
    res.status(500).json({ message: error.message });
  }
};

// Get patients by university (for assistants)
const getPatientsByUniversity = async (req, res) => {
  try {
    const { university } = req.params;
    
    if (!university) {
      return res.status(400).json({ message: 'University parameter is required' });
    }

    // First get all students in this university
    const students = await FirestoreHelpers.find(
      COLLECTIONS.STUDENTS,
      { field: 'university', operator: '==', value: university }
    );

    if (!students || students.length === 0) {
      return res.status(404).json({ message: 'No students found for this university' });
    }

    // Get student IDs
    const studentIds = students.map(student => student.studentId);

    // Get all patients for these students
    const allPatients = await FirestoreHelpers.find(COLLECTIONS.PATIENTS);
    const universityPatients = allPatients.filter(patient => 
      studentIds.includes(patient.drId)
    );

    console.log(`Found ${universityPatients.length} patients for university ${university}`);
    res.json(universityPatients);
  } catch (error) {
    console.error('Error getting patients by university:', error);
    res.status(500).json({
      message: 'Server error while fetching patients',
      error: error.message
    });
  }
};

// Update patient medical info
const updatePatientMedicalInfo = async (req, res) => {
  try {
    const { nationalId } = req.params;
    const { medicalInfo } = req.body;

    const patient = await FirestoreHelpers.findOne(
      COLLECTIONS.PATIENTS,
      { field: 'nationalId', operator: '==', value: nationalId }
    );

    if (!patient) {
      return res.status(404).json({ message: 'Patient not found' });
    }

    patient.medicalInfo = medicalInfo;
    await FirestoreHelpers.update(
      COLLECTIONS.PATIENTS,
      patient.id,
      { medicalInfo: patient.medicalInfo }
    );

    res.json(patient);
  } catch (error) {
    console.error('Error updating patient medical info:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// Delete patient
const deletePatient = async (req, res) => {
  try {
    const patient = await FirestoreHelpers.findOne(
      COLLECTIONS.PATIENTS,
      { field: 'nationalId', operator: '==', value: req.params.nationalId }
    );
    if (!patient) {
      return res.status(404).json({ message: 'Patient not found' });
    }
    
    await FirestoreHelpers.delete(COLLECTIONS.PATIENTS, patient.id);
    res.json({ message: 'Patient deleted successfully' });
  } catch (error) {
    console.error('Error deleting patient:', error);
    res.status(500).json({
      message: 'Server error while deleting patient',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get treatment sheet by type
const getTreatmentSheetByType = async (req, res) => {
  try {
    const { nationalId, sheetType } = req.params;

    console.log(`Fetching sheet of type '${sheetType}' for patient with nationalId '${nationalId}'`);

    const patient = await FirestoreHelpers.findOne(
      COLLECTIONS.PATIENTS,
      { field: 'nationalId', operator: '==', value: nationalId }
    );

    if (!patient) {
      console.log(`Patient with nationalId '${nationalId}' not found`);
      return res.status(404).json({ message: 'Patient not found' });
    }

    if (!patient.treatmentSheets || patient.treatmentSheets.length === 0) {
      console.log(`No treatment sheets found for patient with nationalId '${nationalId}'`);
      return res.status(404).json({ message: `No treatment sheets found for this patient` });
    }

    console.log(`Found ${patient.treatmentSheets.length} treatment sheets for patient`);

    // Find the most recent sheet of the specified type
    const matchingSheets = patient.treatmentSheets.filter(sheet => sheet.type === sheetType);
    console.log(`Found ${matchingSheets.length} sheets of type '${sheetType}'`);

    if (matchingSheets.length === 0) {
      return res.status(404).json({ message: `No ${sheetType} sheet found for this patient` });
    }

    const sheet = matchingSheets.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))[0];
    console.log(`Returning most recent sheet with id ${sheet._id}`);

    res.json(sheet);
  } catch (error) {
    console.error(`Error fetching ${req.params.sheetType} sheet:`, error);
    res.status(500).json({
      message: `Server error while fetching ${req.params.sheetType} sheet`,
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Update patient consent
const updatePatientConsent = async (req, res) => {
  try {
    const { nationalId } = req.params;
    let { signatureText, signatureImage } = req.body;

    // Handle file upload if present
    if (req.files && req.files.signatures && req.files.signatures.length > 0) {
      const uploadedFile = req.files.signatures[0];
      signatureImage = getFileUrl(uploadedFile);
    }

    // Validate that at least one signature method is provided
    if (!signatureText && !signatureImage) {
      return res.status(400).json({ message: 'At least one signature method (text or image) is required' });
    }

    let finalSignatureImage = signatureImage;

    // If signatureImage is a base64 data URL, upload it to Cloudinary
    if (signatureImage && signatureImage.startsWith('data:image')) {
      try {
        if (isCloudinaryConfigured()) {
          // Upload to Cloudinary
          const cloudinary = require('../config/cloudinary').cloudinary;
          const uploadResult = await cloudinary.uploader.upload(signatureImage, {
            folder: 'odenta/signatures',
            resource_type: 'image',
            transformation: [
              { width: 400, height: 200, crop: 'limit' },
              { quality: 'auto' },
              { fetch_format: 'auto' }
            ]
          });
          finalSignatureImage = uploadResult.secure_url;
        } else {
          // For local storage, keep the base64 data URL
          finalSignatureImage = signatureImage;
        }
      } catch (uploadError) {
        console.error('Error uploading signature to Cloudinary:', uploadError);
        // Fallback to base64 if Cloudinary upload fails
        finalSignatureImage = signatureImage;
      }
    }

    const consentData = {
      signatureText: signatureText || '',
      signatureImage: finalSignatureImage || '',
      signedAt: new Date(),
      isSigned: true
    };

    const patient = await FirestoreHelpers.findOne(
      COLLECTIONS.PATIENTS,
      { field: 'nationalId', operator: '==', value: nationalId }
    );

    if (!patient) {
      return res.status(404).json({ message: 'Patient not found' });
    }

    patient.consent = consentData;
    await FirestoreHelpers.update(
      COLLECTIONS.PATIENTS,
      patient.id,
      { consent: patient.consent }
    );

    res.json({
      message: 'Consent updated successfully',
      consent: patient.consent
    });
  } catch (error) {
    console.error('Error updating patient consent:', error);
    res.status(500).json({
      message: 'Server error while updating consent',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  createPatient,
  getPatientById,
  updatePatient,
  addXray,
  addGalleryImage,
  updateXrayNote,
  updateGalleryImageNote,
  deleteXray,
  deleteGalleryImage,
  getPatientsByDoctor,
  updatePatientMedicalInfo,
  deletePatient,
  addTreatmentSheet,
  getTreatmentSheets,
  updateTreatmentSheet,
  deleteTreatmentSheet,
  getTreatmentSheetByType,
  updatePatientConsent,
  getPatientsByUniversity
};