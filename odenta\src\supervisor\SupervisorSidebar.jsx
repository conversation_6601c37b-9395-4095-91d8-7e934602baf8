import { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FaHome, FaClipboardCheck, FaChartBar, FaSignOutAlt, FaUserMd, FaTooth } from 'react-icons/fa';
import { useAuth } from '../context/AuthContext';

// Website color palette
const colorPalette = {
  primary: '#0077B6',
  secondary: '#20B2AA',
  background: '#FFFFFF',
  text: '#333333',
  accent: '#28A745'
};

const SupervisorSidebar = ({ isOpen, toggleSidebar }) => {
  const location = useLocation();
  const { logout } = useAuth();
  const [isHovered, setIsHovered] = useState(false);

  const sidebarVariants = {
    open: { width: '250px', transition: { duration: 0.3 } },
    closed: { width: '80px', transition: { duration: 0.3 } },
  };

  const textVariants = {
    open: { opacity: 1, display: 'block', transition: { delay: 0.1 } },
    closed: { opacity: 0, display: 'none', transition: { duration: 0.1 } },
  };

  const isActive = (path) => location.pathname === path;

  const navItems = [
    { path: '/supervisor', icon: <FaHome />, text: 'Dashboard' },
    { path: '/supervisor/reviews', icon: <FaClipboardCheck />, text: 'Reviews' },
    { path: '/supervisor/analytics', icon: <FaChartBar />, text: 'Analytics' },
    { path: '/supervisor/profile', icon: <FaUserMd />, text: 'Profile' },
  ];

  return (
    <motion.div
      className="h-screen flex flex-col shadow-xl z-20"
      style={{ backgroundColor: colorPalette.primary, color: colorPalette.background }}
      variants={sidebarVariants}
      animate={isOpen ? 'open' : 'closed'}
      initial={isOpen ? 'open' : 'closed'}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="p-4 flex items-center justify-center border-b" style={{ borderColor: `${colorPalette.primary}` }}>
          <Link to="/">
                    <img 
                      src="/imgs/odenta-logo2.jpg" // Update this path based on your project structure
                      alt="ODenta Logo"
                      className="h-10 w-auto" // Adjust size as needed
                    />
            </Link>
      </div>

      <div className="flex-1 py-6 flex flex-col justify-between">
        <nav>
          <ul className="space-y-2">
            {navItems.map((item) => (
              <li key={item.path}>
                <Link
                  to={item.path}
                  className="flex items-center px-4 py-3 transition-colors duration-200"
                  style={{
                    backgroundColor: isActive(item.path) ? colorPalette.secondary : 'transparent',
                    color: isActive(item.path) ? colorPalette.background : 'rgba(255, 255, 255, 0.8)'
                  }}
                  onMouseOver={(e) => !isActive(item.path) && (e.currentTarget.style.backgroundColor = `${colorPalette.secondary}80`)}
                  onMouseOut={(e) => !isActive(item.path) && (e.currentTarget.style.backgroundColor = 'transparent')}
                >
                  <span className="text-xl">{item.icon}</span>
                  <motion.span
                    variants={textVariants}
                    animate={(isOpen || isHovered) ? 'open' : 'closed'}
                    className="ml-4"
                  >
                    {item.text}
                  </motion.span>
                </Link>
              </li>
            ))}
          </ul>
        </nav>

        <div className="px-4">
          <button
            onClick={logout}
            className="w-full flex items-center px-4 py-3 transition-colors duration-200 rounded-lg"
            style={{ color: 'rgba(255, 255, 255, 0.8)' }}
            onMouseOver={(e) => (e.currentTarget.style.backgroundColor = `${colorPalette.secondary}80`)}
            onMouseOut={(e) => (e.currentTarget.style.backgroundColor = 'transparent')}
          >
            <FaSignOutAlt className="text-xl" />
            <motion.span
              variants={textVariants}
              animate={(isOpen || isHovered) ? 'open' : 'closed'}
              className="ml-4"
            >
              Logout
            </motion.span>
          </button>
        </div>
      </div>
    </motion.div>
  );
};

export default SupervisorSidebar;
