#!/bin/bash

# Update dependencies and regenerate package-lock.json
echo "Updating dependencies and regenerating package-lock.json..."

# Remove existing node_modules and package-lock.json
rm -rf node_modules package-lock.json

# Install dependencies with legacy peer deps
npm install --legacy-peer-deps

# Verify installation
echo "Verifying installation..."
npm list --depth=0

echo "Dependencies updated successfully!"
echo "Please commit the new package-lock.json file." 