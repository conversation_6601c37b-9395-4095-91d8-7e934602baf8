/**
 * Utility functions for PDF generation
 */
import { jsPDF } from 'jspdf';

/**
 * Prints the specified element as a PDF
 * @param {string} elementId - The ID of the element to print
 * @param {string} filename - The name of the PDF file
 */
export const printElementAsPDF = (elementId, filename = 'download.pdf') => {
  const element = document.getElementById(elementId);
  if (!element) {
    console.error(`Element with ID ${elementId} not found`);
    return;
  }

  // Store the original styles
  const originalDisplay = element.style.display;
  const originalPosition = element.style.position;
  const originalZIndex = element.style.zIndex;

  // Prepare the element for printing
  element.style.display = 'block';
  element.style.position = 'absolute';
  element.style.zIndex = '-1000';

  // Create an iframe to print from
  const iframe = document.createElement('iframe');
  iframe.style.display = 'none';
  document.body.appendChild(iframe);

  iframe.contentWindow.document.open();
  iframe.contentWindow.document.write(`
    <html>
      <head>
        <title>${filename}</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            padding: 20px;
          }
          table {
            width: 100%;
            border-collapse: collapse;
          }
          th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
          }
          th {
            background-color: #f2f2f2;
          }
          .header {
            text-align: center;
            margin-bottom: 20px;
          }
          .header h1 {
            color: #0077B6;
            margin-bottom: 5px;
          }
          .section {
            margin-bottom: 20px;
          }
          .section h2 {
            color: #0077B6;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
          }
          @media print {
            body {
              padding: 0;
              margin: 0;
            }
          }
        </style>
      </head>
      <body>
        ${element.innerHTML}
      </body>
    </html>
  `);
  iframe.contentWindow.document.close();

  // Wait for the iframe to load
  iframe.onload = () => {
    // Print the iframe
    iframe.contentWindow.focus();
    iframe.contentWindow.print();

    // Clean up
    setTimeout(() => {
      document.body.removeChild(iframe);
      element.style.display = originalDisplay;
      element.style.position = originalPosition;
      element.style.zIndex = originalZIndex;
    }, 1000);
  };
};

/**
 * Creates a download button for PDF
 * @param {string} elementId - The ID of the element to print
 * @param {string} filename - The name of the PDF file
 * @returns {JSX.Element} - A button element
 */
export const createPDFDownloadButton = (elementId, filename) => {
  return (
    <button
      onClick={() => printElementAsPDF(elementId, filename)}
      className="px-4 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full font-medium transition-all duration-300 shadow-md hover:shadow-lg flex items-center"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="h-5 w-5 mr-2"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
        />
      </svg>
      Download PDF
    </button>
  );
};

/**
 * Generates a PDF for a treatment sheet
 * @param {Object} sheet - The sheet object containing all sheet data
 * @param {string} nationalId - The patient's national ID
 */
export const generateSheetPDF = async (sheet, nationalId) => {
  try {
    const doc = new jsPDF({ orientation: 'portrait', unit: 'mm', format: 'a4' });

    // Helper function to add header
    const addHeader = (pageNum) => {
      doc.setFillColor(0, 119, 182); // #0077B6
      doc.rect(0, 0, 210, 25, 'F');
      doc.setFontSize(20);
      doc.setTextColor(255, 255, 255);
      doc.setFont('helvetica', 'bold');
      doc.text('ODenta - Treatment Sheet', 105, 12, { align: 'center' });
      doc.setFontSize(10);
      doc.setTextColor(220, 220, 220);
      doc.text(`Generated: ${new Date().toLocaleString()}`, 10, 20);
      doc.text(`Page ${pageNum}`, 200, 20, { align: 'right' });
    };

    // Add header
    addHeader(1);

    // Sheet information
    doc.setFontSize(16);
    doc.setTextColor(0, 119, 182);
    doc.setFont('helvetica', 'bold');
    doc.text(`${sheet.type} Sheet`, 20, 35);

    doc.setDrawColor(200);
    doc.line(20, 37, 190, 37);

    // Patient and sheet details
    doc.setFontSize(11);
    doc.setTextColor(0);
    doc.setFont('helvetica', 'normal');

    let yPos = 45;
    const lineHeight = 6;

    const patientInfo = [
      `Patient ID: ${nationalId}`,
      `Sheet Type: ${sheet.type}`,
      `Created: ${new Date(sheet.createdAt).toLocaleDateString()} at ${new Date(sheet.createdAt).toLocaleTimeString()}`,
      ...(sheet.updatedAt && sheet.updatedAt !== sheet.createdAt ? [`Updated: ${new Date(sheet.updatedAt).toLocaleDateString()} at ${new Date(sheet.updatedAt).toLocaleTimeString()}`] : [])
    ];

    patientInfo.forEach(info => {
      doc.text(info, 20, yPos);
      yPos += lineHeight;
    });

    yPos += 5;

    // Diagnosis section
    if (sheet.details?.diagnosis) {
      doc.setFontSize(14);
      doc.setTextColor(0, 119, 182);
      doc.setFont('helvetica', 'bold');
      doc.text('Diagnosis', 20, yPos);
      yPos += 8;

      doc.setFontSize(10);
      doc.setTextColor(0);
      doc.setFont('helvetica', 'normal');
      const diagnosisLines = doc.splitTextToSize(sheet.details.diagnosis, 170);
      diagnosisLines.forEach(line => {
        if (yPos > 270) {
          doc.addPage();
          addHeader(doc.internal.getNumberOfPages());
          yPos = 35;
        }
        doc.text(line, 20, yPos);
        yPos += 5;
      });
      yPos += 5;
    }

    // Treatment Plan section
    if (sheet.details?.treatmentPlan) {
      doc.setFontSize(14);
      doc.setTextColor(0, 119, 182);
      doc.setFont('helvetica', 'bold');
      doc.text('Treatment Plan', 20, yPos);
      yPos += 8;

      doc.setFontSize(10);
      doc.setTextColor(0);
      doc.setFont('helvetica', 'normal');
      const treatmentLines = doc.splitTextToSize(sheet.details.treatmentPlan, 170);
      treatmentLines.forEach(line => {
        if (yPos > 270) {
          doc.addPage();
          addHeader(doc.internal.getNumberOfPages());
          yPos = 35;
        }
        doc.text(line, 20, yPos);
        yPos += 5;
      });
      yPos += 5;
    }

    // Notes section
    if (sheet.details?.notes) {
      doc.setFontSize(14);
      doc.setTextColor(0, 119, 182);
      doc.setFont('helvetica', 'bold');
      doc.text('Notes', 20, yPos);
      yPos += 8;

      doc.setFontSize(10);
      doc.setTextColor(0);
      doc.setFont('helvetica', 'normal');
      const notesLines = doc.splitTextToSize(sheet.details.notes, 170);
      notesLines.forEach(line => {
        if (yPos > 270) {
          doc.addPage();
          addHeader(doc.internal.getNumberOfPages());
          yPos = 35;
        }
        doc.text(line, 20, yPos);
        yPos += 5;
      });
      yPos += 5;
    }

    // Detailed data section
    if (sheet.details?.specificData && Object.keys(sheet.details.specificData).length > 0) {
      doc.setFontSize(14);
      doc.setTextColor(0, 119, 182);
      doc.setFont('helvetica', 'bold');
      doc.text('Detailed Information', 20, yPos);
      yPos += 8;

      const addObjectToPDF = (obj, level = 0) => {
        Object.entries(obj).forEach(([key, value]) => {
          if (value === null || value === undefined || value === '') return;

          if (yPos > 270) {
            doc.addPage();
            addHeader(doc.internal.getNumberOfPages());
            yPos = 35;
          }

          const indent = 20 + (level * 10);

          if (typeof value === 'object' && !Array.isArray(value)) {
            doc.setFontSize(11);
            doc.setTextColor(32, 178, 170); // #20B2AA
            doc.setFont('helvetica', 'bold');
            doc.text(key.replace(/([A-Z])/g, ' $1').trim(), indent, yPos);
            yPos += 6;
            addObjectToPDF(value, level + 1);
          } else if (Array.isArray(value)) {
            doc.setFontSize(11);
            doc.setTextColor(32, 178, 170);
            doc.setFont('helvetica', 'bold');
            doc.text(key.replace(/([A-Z])/g, ' $1').trim(), indent, yPos);
            yPos += 6;
            value.forEach((item, index) => {
              if (yPos > 270) {
                doc.addPage();
                addHeader(doc.internal.getNumberOfPages());
                yPos = 35;
              }
              doc.setFontSize(10);
              doc.setTextColor(0);
              doc.setFont('helvetica', 'normal');
              doc.text(`${index + 1}. ${typeof item === 'object' ? JSON.stringify(item) : item}`, indent + 5, yPos);
              yPos += 5;
            });
          } else {
            doc.setFontSize(10);
            doc.setTextColor(0);
            doc.setFont('helvetica', 'normal');
            const text = `${key.replace(/([A-Z])/g, ' $1').trim()}: ${value}`;
            const lines = doc.splitTextToSize(text, 170 - (level * 10));
            lines.forEach(line => {
              if (yPos > 270) {
                doc.addPage();
                addHeader(doc.internal.getNumberOfPages());
                yPos = 35;
              }
              doc.text(line, indent, yPos);
              yPos += 5;
            });
          }
        });
      };

      addObjectToPDF(sheet.details.specificData);
    }

    // Add page numbers
    const pageCount = doc.internal.getNumberOfPages();
    for (let i = 1; i <= pageCount; i++) {
      doc.setPage(i);
      doc.setFontSize(10);
      doc.setTextColor(150);
      doc.text(`Page ${i} of ${pageCount}`, 105, 287, { align: 'center' });
    }

    // Generate filename and download
    const filename = `${sheet.type.replace(/\s+/g, '_')}_Sheet_${nationalId}_${new Date(sheet.createdAt).toLocaleDateString().replace(/\//g, '-')}.pdf`;
    doc.save(filename);

  } catch (error) {
    console.error('Error generating PDF:', error);
    throw error;
  }
};
