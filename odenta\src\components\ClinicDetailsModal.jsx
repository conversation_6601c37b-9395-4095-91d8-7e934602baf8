import { motion, AnimatePresence } from 'framer-motion';
import { FaTimes, FaClinicMedical, FaPhone, FaEnvelope, FaGlobe, FaMapMarkerAlt, FaClock, FaCalendarAlt, FaImage, FaList } from 'react-icons/fa';

const ClinicDetailsModal = ({ isOpen, onClose, clinic }) => {
  if (!clinic) return null;

  // Function to format text with proper line breaks
  const formatText = (text) => {
    return text ? text.split('\n').map((line, i) => (
      <span key={i}>
        {line}
        <br />
      </span>
    )) : 'N/A';
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50 p-4"
          onClick={onClose}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0, y: 20 }}
            animate={{ scale: 1, opacity: 1, y: 0 }}
            exit={{ scale: 0.9, opacity: 0, y: 20 }}
            transition={{ type: "spring", damping: 25, stiffness: 300 }}
            className="bg-white rounded-2xl shadow-2xl max-w-5xl w-full mx-4 max-h-[90vh] overflow-y-auto relative"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header with clinic logo and name */}
            <div className="sticky top-0 z-10 bg-gradient-to-r from-blue-600 to-blue-800 text-white p-6 rounded-t-2xl flex justify-between items-center">
              <div className="flex items-center">
                {clinic.logo ? (
                  <img
                    src={clinic.logo}
                    alt={clinic.name?.en || 'Clinic Logo'}
                    className="h-12 w-12 rounded-full bg-white p-1 mr-4 object-contain"
                  />
                ) : (
                  <div className="h-12 w-12 rounded-full bg-white flex items-center justify-center mr-4">
                    <FaClinicMedical className="h-6 w-6 text-blue-800" />
                  </div>
                )}
                <div>
                  <h2 className="text-2xl font-bold">{clinic.name?.en || 'N/A'}</h2>
                  <p className="text-blue-100 text-sm">{clinic.dentistId || 'N/A'}</p>
                </div>
              </div>
              <button
                onClick={onClose}
                className="text-white hover:text-gray-200 transition-colors bg-blue-700 hover:bg-blue-800 p-2 rounded-full"
              >
                <FaTimes className="h-6 w-6" />
              </button>
            </div>

            {/* Main content with tabs */}
            <div className="p-0 bg-gray-50">
              {/* Clinic image banner if available */}
              {clinic.image && (
                <div className="w-full h-48 md:h-64 overflow-hidden">
                  <img
                    src={clinic.image}
                    alt={clinic.name?.en || 'Clinic'}
                    className="w-full h-full object-cover"
                  />
                </div>
              )}

              <div className="p-6 space-y-6">
                {/* Quick Info Cards */}
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                  <div className="bg-white p-4 rounded-xl shadow-sm border border-gray-100 flex items-start">
                    <div className="bg-blue-50 p-3 rounded-lg mr-3">
                      <FaPhone className="text-blue-600" />
                    </div>
                    <div>
                      <h4 className="text-sm font-semibold text-gray-500">Phone</h4>
                      <p className="text-gray-800">{clinic.contactInfo?.phone || 'N/A'}</p>
                    </div>
                  </div>

                  <div className="bg-white p-4 rounded-xl shadow-sm border border-gray-100 flex items-start">
                    <div className="bg-blue-50 p-3 rounded-lg mr-3">
                      <FaEnvelope className="text-blue-600" />
                    </div>
                    <div>
                      <h4 className="text-sm font-semibold text-gray-500">Email</h4>
                      <p className="text-gray-800 truncate">{clinic.contactInfo?.email || 'N/A'}</p>
                    </div>
                  </div>

                  <div className="bg-white p-4 rounded-xl shadow-sm border border-gray-100 flex items-start">
                    <div className="bg-blue-50 p-3 rounded-lg mr-3">
                      <FaMapMarkerAlt className="text-blue-600" />
                    </div>
                    <div>
                      <h4 className="text-sm font-semibold text-gray-500">Location</h4>
                      <p className="text-gray-800">{clinic.address?.city?.en || 'N/A'}, {clinic.address?.country?.en || 'N/A'}</p>
                    </div>
                  </div>
                </div>

                {/* Main Content Sections */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {/* Left Column */}
                  <div className="md:col-span-2 space-y-6">
                    {/* About Section */}
                    <section className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                      <h3 className="text-xl font-semibold text-blue-900 mb-4 flex items-center">
                        <FaClinicMedical className="mr-2 text-blue-700" /> About Clinic
                      </h3>
                      <div className="prose max-w-none text-gray-700">
                        <h4 className="text-lg font-medium text-blue-800 mb-2">Clinic Name</h4>
                        <p className="mb-4">{clinic.clinicName?.en || 'N/A'}</p>

                        <h4 className="text-lg font-medium text-blue-800 mt-6 mb-2">About</h4>
                        <p className="mb-4">{formatText(clinic.about?.en)}</p>
                      </div>
                    </section>

                    {/* Services */}
                    <section className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                      <h3 className="text-xl font-semibold text-blue-900 mb-4 flex items-center">
                        <FaList className="mr-2 text-blue-700" /> Services
                      </h3>
                      {clinic.services?.length > 0 ? (
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                          {clinic.services.map((service, index) => (
                            <div key={index} className="bg-blue-50 p-3 rounded-lg text-blue-800">
                              <span className="font-medium">{service.en || 'N/A'}</span>
                              <span className="text-sm text-blue-600 block mt-1 rtl:text-right">{service.ar || 'N/A'}</span>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <p className="text-gray-500 italic">No services available.</p>
                      )}
                    </section>
                  </div>

                  {/* Right Column */}
                  <div className="space-y-6">
                    {/* Contact Information */}
                    <section className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                      <h3 className="text-xl font-semibold text-blue-900 mb-4 flex items-center">
                        <FaGlobe className="mr-2 text-blue-700" /> Contact & Links
                      </h3>
                      <ul className="space-y-4">
                        <li className="flex items-start">
                          <FaPhone className="text-blue-600 mt-1 mr-3" />
                          <div>
                            <p className="text-sm font-medium text-gray-500">Phone</p>
                            <p className="text-gray-800">{clinic.contactInfo?.phone || 'N/A'}</p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <FaEnvelope className="text-blue-600 mt-1 mr-3" />
                          <div>
                            <p className="text-sm font-medium text-gray-500">Email</p>
                            <p className="text-gray-800 break-all">{clinic.contactInfo?.email || 'N/A'}</p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <FaGlobe className="text-blue-600 mt-1 mr-3" />
                          <div>
                            <p className="text-sm font-medium text-gray-500">Website</p>
                            {clinic.contactInfo?.website ? (
                              <a
                                href={clinic.contactInfo.website}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-blue-600 hover:underline"
                              >
                                Visit Website
                              </a>
                            ) : (
                              <p className="text-gray-500">N/A</p>
                            )}
                          </div>
                        </li>
                        <li className="flex items-start">
                          <FaMapMarkerAlt className="text-blue-600 mt-1 mr-3" />
                          <div>
                            <p className="text-sm font-medium text-gray-500">Address</p>
                            <p className="text-gray-800">
                              {clinic.address?.street?.en || 'N/A'}, {clinic.address?.city?.en || 'N/A'}, {clinic.address?.country?.en || 'N/A'} {clinic.address?.postalCode || ''}
                            </p>
                          </div>
                        </li>
                      </ul>
                    </section>

                    {/* Working Hours */}
                    <section className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                      <h3 className="text-xl font-semibold text-blue-900 mb-4 flex items-center">
                        <FaClock className="mr-2 text-blue-700" /> Working Hours
                      </h3>
                      {clinic.workingHours ? (
                        <ul className="space-y-2">
                          {Object.entries(clinic.workingHours).map(([day, hours]) => (
                            <li key={day} className="flex justify-between items-center py-1 border-b border-gray-100 last:border-0">
                              <span className="font-medium capitalize">{day}</span>
                              <span className="text-gray-700">{hours || 'Closed'}</span>
                            </li>
                          ))}
                        </ul>
                      ) : (
                        <p className="text-gray-500 italic">No working hours available.</p>
                      )}
                    </section>

                    {/* Slot Information */}
                    <section className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                      <h3 className="text-xl font-semibold text-blue-900 mb-4 flex items-center">
                        <FaClock className="mr-2 text-blue-700" /> Slot Information
                      </h3>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <FaCalendarAlt className="text-blue-600 mr-2" />
                            <span className="text-sm font-medium text-gray-500">Slot Period</span>
                          </div>
                          <span className="text-gray-800">
                            {clinic.slotBeginDate ? new Date(clinic.slotBeginDate).toLocaleDateString() : 'N/A'} - {clinic.slotEndDate ? new Date(clinic.slotEndDate).toLocaleDateString() : 'N/A'}
                          </span>
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <FaClock className="text-blue-600 mr-2" />
                            <span className="text-sm font-medium text-gray-500">Slot Duration</span>
                          </div>
                          <span className="text-gray-800">
                            {clinic && typeof clinic.slotDuration !== 'undefined' && clinic.slotDuration !== null
                              ? `${clinic.slotDuration} minutes`
                              : 'N/A'}
                          </span>
                        </div>

                        <div>
                          <div className="flex items-center mb-2">
                            <FaClock className="text-blue-600 mr-2" />
                            <span className="text-sm font-medium text-gray-500">Available Time Slots</span>
                          </div>

                          {clinic.timeSlots && clinic.timeSlots.length > 0 ? (
                            <div className="flex flex-wrap gap-2 mt-2">
                              {[...new Set(clinic.timeSlots.map(slot => slot.time))].map((time, index) => (
                                <span key={index} className="bg-blue-50 text-blue-700 px-3 py-1 rounded-full text-sm">
                                  {time}
                                </span>
                              ))}
                            </div>
                          ) : (
                            <p className="text-gray-500 italic">No time slots available.</p>
                          )}
                        </div>
                      </div>
                    </section>
                  </div>
                </div>
              </div>
            </div>

            <div className="sticky bottom-0 bg-white p-6 border-t rounded-b-2xl flex justify-end">
              <button
                onClick={onClose}
                className="px-6 py-2 bg-gradient-to-r from-blue-500 to-blue-700 text-white rounded-lg hover:from-blue-600 hover:to-blue-800 transition-colors shadow-md hover:shadow-lg font-medium"
              >
                Close
              </button>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default ClinicDetailsModal;
