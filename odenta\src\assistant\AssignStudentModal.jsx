import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaTimes, FaUserMd, FaS<PERSON>ch, FaSpinner } from 'react-icons/fa';
import axios from 'axios';
import { useAuth } from '../context/AuthContext';

// Website color palette
const colorPalette = {
  primary: '#0077B6',
  secondary: '#20B2AA',
  background: '#FFFFFF',
  text: '#333333',
  accent: '#28A745'
};

const AssignStudentModal = ({ isOpen, onClose, appointment, onAssign }) => {
  const [students, setStudents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [studentAvailability, setStudentAvailability] = useState({});
  const { token, user } = useAuth();

  useEffect(() => {
    if (isOpen && appointment) {
      fetchStudents();
    }
  }, [isOpen, appointment, token, user]);

  const fetchStudents = async () => {
    setLoading(true);
    try {
      const universityId = user.university || user.affiliation?.id;
      const config = { headers: { Authorization: `Bearer ${token}` } };

      // Use the public endpoint to get all students
      const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/students`, config);

      // Get the appointment date and time
      const appointmentDate = new Date(appointment.date);
      const appointmentTime = appointment.time;

      // Create a map to store student availability
      const availabilityMap = {};

      // Fetch all appointments to check for conflicts
      const appointmentsResponse = await axios.get(
        `${process.env.REACT_APP_API_URL}/api/appointments/schedule?university=${encodeURIComponent(universityId)}`,
        config
      );

      const allAppointments = appointmentsResponse.data || [];

      // Check each student's availability
      response.data.forEach(student => {
        // Find appointments for this student
        const studentAppointments = allAppointments.filter(
          appt => appt.doctor === student.studentId &&
                 appt._id !== appointment._id &&
                 appt.status !== 'cancelled' // Ignore cancelled appointments
        );

        // Check if any appointment conflicts with the current one
        const hasConflict = studentAppointments.some(appt => {
          const apptDate = new Date(appt.date);
          const currentDate = new Date(appointmentDate);

          // Compare dates by converting to same format
          const apptDateString = apptDate.toISOString().split('T')[0];
          const currentDateString = currentDate.toISOString().split('T')[0];

          // Check if same day and same time slot
          return apptDateString === currentDateString && appt.time === appointmentTime;
        });

        console.log(`Student ${student.name} (${student.studentId}) availability check:`, {
          appointmentDate: appointmentDate,
          appointmentTime: appointmentTime,
          conflictingAppointments: studentAppointments.filter(appt => {
            const apptDate = new Date(appt.date);
            const currentDate = new Date(appointmentDate);
            const apptDateString = apptDate.toISOString().split('T')[0];
            const currentDateString = currentDate.toISOString().split('T')[0];
            return apptDateString === currentDateString && appt.time === appointmentTime;
          }),
          hasConflict: hasConflict
        });

        // Store availability status
        availabilityMap[student.studentId] = !hasConflict;
      });

      setStudentAvailability(availabilityMap);
      setStudents(response.data || []);
      setError('');
    } catch (err) {
      console.error('Error fetching students:', err.response?.data || err.message);
      setError('Failed to load students. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleAssign = async () => {
    if (!selectedStudent) {
      setError('Please select a student to assign.');
      return;
    }

    // Check if the selected student is available
    if (!studentAvailability[selectedStudent.studentId]) {
      setError('This student is not available at the selected time. Please choose another student.');
      return;
    }

    try {
      // Call the onAssign callback with the selected student
      // Use the correct ID field (could be _id, id, or appointmentId)
      const appointmentId = appointment._id || appointment.id || appointment.appointmentId;
      console.log('Assigning appointment with ID:', appointmentId);
      await onAssign(appointmentId, selectedStudent);
      onClose();
    } catch (err) {
      console.error('Error in handleAssign:', err);

      // Check if this is a conflict error from the backend
      if (err.response?.data?.conflict) {
        setError('This student already has an appointment at this time. Please choose another student.');

        // Update the availability status for this student
        setStudentAvailability(prev => ({
          ...prev,
          [selectedStudent.studentId]: false
        }));
      } else {
        setError('Failed to assign student. Please try again.');
      }
    }
  };

  const filteredStudents = students.filter(student =>
    student.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    student.studentId?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50 p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0, y: 20 }}
          animate={{ scale: 1, opacity: 1, y: 0 }}
          exit={{ scale: 0.9, opacity: 0, y: 20 }}
          transition={{ type: "spring", damping: 25, stiffness: 300 }}
          className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto relative"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="sticky top-0 z-10 text-white p-6 rounded-t-2xl flex justify-between items-center"
            style={{ background: `linear-gradient(to right, ${colorPalette.primary}, ${colorPalette.secondary})` }}>
            <div className="flex items-center">
              <FaUserMd className="h-6 w-6 mr-3" />
              <h2 className="text-xl font-bold">Assign Student</h2>
            </div>
            <button
              onClick={onClose}
              className="text-white hover:text-gray-200 transition-colors"
            >
              <FaTimes className="h-6 w-6" />
            </button>
          </div>

          {/* Main content */}
          <div className="p-6">
            {error && (
              <div className="mb-4 p-3 bg-red-50 border-l-4 border-red-500 text-red-700 rounded">
                {error}
              </div>
            )}

            {/* Search bar */}
            <div className="mb-6">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FaSearch className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg"
                  style={{
                    outline: 'none',
                    boxShadow: 'none',
                    borderColor: '#e5e7eb',
                    ':focus': { borderColor: colorPalette.primary }
                  }}
                  placeholder="Search students by name or ID..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>

            {/* Students list */}
            <div className="mb-6">
              {loading ? (
                <div className="flex justify-center items-center py-8">
                  <FaSpinner className="h-8 w-8 animate-spin" style={{ color: colorPalette.primary }} />
                </div>
              ) : filteredStudents.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  No students available. Try a different search term.
                </div>
              ) : (
                <div className="grid grid-cols-1 gap-3 max-h-[40vh] overflow-y-auto pr-2">
                  {filteredStudents.map((student) => (
                    <div
                      key={student._id || student.studentId}
                      className={`p-4 border rounded-lg transition-colors ${
                        studentAvailability[student.studentId]
                          ? 'cursor-pointer ' + (selectedStudent?.studentId !== student.studentId ? 'hover:bg-gray-50' : '')
                          : 'opacity-70 cursor-not-allowed'
                      }`}
                      style={{
                        backgroundColor: selectedStudent?.studentId === student.studentId
                          ? `${colorPalette.primary}10`
                          : !studentAvailability[student.studentId]
                            ? '#f9fafb'
                            : colorPalette.background,
                        borderColor: selectedStudent?.studentId === student.studentId
                          ? colorPalette.primary
                          : !studentAvailability[student.studentId]
                            ? '#ef444430'
                            : '#e5e7eb'
                      }}
                      onClick={() => {
                        // Only allow selecting available students
                        if (studentAvailability[student.studentId]) {
                          setSelectedStudent(student);
                          setError('');
                        } else {
                          setError('This student is not available at the selected time. Please choose another student.');
                        }
                      }}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div className="rounded-full p-2 mr-3" style={{ backgroundColor: `${colorPalette.primary}15` }}>
                            <FaUserMd className="h-5 w-5" style={{ color: colorPalette.primary }} />
                          </div>
                          <div>
                            <h3 className="font-medium text-gray-900">{student.name}</h3>
                            <p className="text-sm text-gray-500">ID: {student.studentId}</p>
                          </div>
                        </div>
                        <div>
                          {studentAvailability[student.studentId] ? (
                            <span className="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Available</span>
                          ) : (
                            <span className="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">Not Available</span>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Footer with action buttons */}
          <div className="sticky bottom-0 bg-white p-6 border-t rounded-b-2xl flex justify-end space-x-3">
            <button
              onClick={onClose}
              className="px-6 py-2 border rounded-lg font-medium transition-colors hover:bg-gray-50"
              style={{
                borderColor: '#e5e7eb',
                color: colorPalette.text
              }}
            >
              Cancel
            </button>
            <button
              onClick={handleAssign}
              disabled={!selectedStudent || loading}
              className="px-6 py-2 text-white rounded-lg transition-colors shadow-md hover:shadow-lg font-medium hover:brightness-110"
              style={{
                background: `linear-gradient(to right, ${colorPalette.primary}, ${colorPalette.secondary})`,
                opacity: !selectedStudent || loading ? 0.5 : 1,
                cursor: !selectedStudent || loading ? 'not-allowed' : 'pointer'
              }}
            >
              Assign
            </button>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default AssignStudentModal;
