import { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import axios from 'axios';
import { useAuth } from '../context/AuthContext';
import Navbar from './Navbar';
import Sidebar from './Sidebar';
import Loader from '../components/Loader';
import SuccessModal from '../components/SuccessModal';
import ProcedureRequestsWidget from './ProcedureRequestsWidget';
import AppointmentsWidget from './AppointmentsWidget';
import { motion } from 'framer-motion';
import { FaTooth, FaCalendarAlt, FaUserMd, FaUniversity, FaClinicMedical, FaCheckCircle } from 'react-icons/fa';
import { MdOutlineSupervisorAccount, MdHealthAndSafety } from 'react-icons/md';

const Dashboard = () => {
  const { user, token } = useAuth();
  const navigate = useNavigate();
  const [showModal, setShowModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [patients, setPatients] = useState([]);
  const [appointments, setAppointments] = useState([]);
  const [procedureRequests, setProcedureRequests] = useState([]);
  const [selectedPatient, setSelectedPatient] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [dayFilter, setDayFilter] = useState('today');
  const [hourFilter, setHourFilter] = useState('');
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [formData, setFormData] = useState({
    procedureType: '',
    notes: '',
  });

  const chronicDiseases = [
    'Diabetes',
    'Hypertension',
    'Heart Disease',
    'Asthma',
    'Arthritis',
    'Thyroid Disorder',
    'Kidney Disease',
    'Liver Disease',
    'Cancer',
    'Epilepsy',
    'HIV/AIDS',
    'Hepatitis',
  ];

  useEffect(() => {
    const fetchData = async () => {
      if (!user?.id || !token) {
        setError('Please log in to view dashboard.');
        setLoading(false);
        return;
      }

      try {
        const config = { headers: { Authorization: `Bearer ${token}` } };
        const [patientsRes, appointmentsRes, procedureRequestsRes] = await Promise.all([
          axios.get(`${process.env.REACT_APP_API_URL}/api/patients`, config),
          axios.get(`${process.env.REACT_APP_API_URL}/api/appointments/my-appointments`, config),
          axios.get(`${process.env.REACT_APP_API_URL}/api/procedure-requests/student`, config),
        ]);

        console.log('Patients response:', patientsRes.data);
        console.log('Appointments response:', appointmentsRes.data);
        console.log('Procedure requests response:', procedureRequestsRes.data);

        setPatients(patientsRes.data || []);
        setProcedureRequests(procedureRequestsRes.data || []);

        if (Array.isArray(appointmentsRes.data)) {
          // Handle date parsing for appointments
          const processedAppointments = appointmentsRes.data.map(appt => {
            let date;
            if (typeof appt.date === 'string') {
              date = new Date(appt.date);
            } else if (appt.date && appt.date.toDate) {
              // Firestore timestamp
              date = appt.date.toDate();
            } else if (appt.date instanceof Date) {
              date = appt.date;
            } else if (appt.date && appt.date._seconds) {
              // Firestore timestamp object
              date = new Date(appt.date._seconds * 1000);
            } else {
              console.warn('Invalid date format for appointment:', appt.date);
              date = new Date();
            }
            
            // Check if the parsed date is valid
            if (isNaN(date.getTime())) {
              console.warn('Invalid date after parsing:', appt.date);
              date = new Date();
            }
            
            return {
              ...appt,
              date: date,
            };
          });
          setAppointments(processedAppointments);
        } else {
          console.error('Invalid appointments data:', appointmentsRes.data);
          setAppointments([]);
        }
      } catch (err) {
        console.error('Fetch error:', err.response?.status, err.response?.data);
        setError(err.response?.data?.message || 'Failed to load dashboard data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [user, token]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleRequestProcedure = async (e) => {
    e.preventDefault();
    if (!user?.id || !token) {
      setError('Please log in to request a procedure.');
      return;
    }
    try {
      const requestData = {
        procedureType: formData.procedureType,
        notes: formData.notes || '',
      };

      const response = await axios.post(`${process.env.REACT_APP_API_URL}/api/procedure-requests`, requestData, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.data && response.data.procedureRequest) {
        // Add the new request to the state
        setProcedureRequests(prev => [response.data.procedureRequest, ...prev]);

        setShowModal(false);
        setFormData({
          procedureType: '',
          notes: '',
        });
        // Show success message
        setError('');
        setSuccessMessage(`Your ${response.data.procedureRequest.procedureType} procedure request has been submitted successfully!`);
        setShowSuccessModal(true);
      } else {
        throw new Error('Invalid response received from server');
      }
    } catch (err) {
      console.error('Procedure request error:', err.response?.status, err.response?.data);
      setError(err.response?.data?.message || 'Failed to submit procedure request');
    }
  };

  const getStatusBadgeClass = (status) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'pending':
      default:
        return 'bg-yellow-100 text-yellow-800';
    }
  };

  const handleViewPatient = (patient) => {
    // Ensure we have a valid patient object
    if (!patient || (!patient.nationalId && !patient._id)) {
      console.error('Invalid patient data:', patient);
      return;
    }
    
    // If patient is just an ID, try to find the full patient object
    if (typeof patient === 'string' || (patient && !patient.fullName)) {
      const foundPatient = patients.find(p => p._id === patient || p.nationalId === patient);
      if (foundPatient) {
        setSelectedPatient(foundPatient);
      } else {
        console.error('Patient not found in patients list:', patient);
      }
    } else {
      setSelectedPatient(patient);
    }
  };

  // Filter appointments based on day and hour filters
  const filteredAppointments = appointments.filter((appt) => {
    const appointmentDate = new Date(appt.date);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const nextWeek = new Date(today);
    nextWeek.setDate(nextWeek.getDate() + 7);

    // Apply day filter
    if (dayFilter === 'today' && appointmentDate.toDateString() !== today.toDateString()) {
      return false;
    }
    if (dayFilter === 'tomorrow' && appointmentDate.toDateString() !== tomorrow.toDateString()) {
      return false;
    }
    if (dayFilter === 'week' && (appointmentDate < today || appointmentDate >= nextWeek)) {
      return false;
    }

    // Apply hour filter
    if (hourFilter && appt.time !== hourFilter) {
      return false;
    }

    return true;
  });

  if (loading) return <Loader />;

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />

      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />

        <main className="flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white">
          <div className="max-w-7xl mx-auto">
            {error && (
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm"
              >
                <div className="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500 mr-3" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  <p className="text-red-700 font-medium">{error}</p>
                </div>
              </motion.div>
            )}

            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
                <div>
                  <h1 className="text-3xl md:text-4xl font-bold text-[#0077B6] mb-1">
                    Welcome, {user?.name || 'Student'}
                  </h1>
                  <p className="text-[#333333]">Here's an overview of your dental practice</p>
                </div>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => setShowModal(true)}
                  className="w-full md:w-auto bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-6 py-3 rounded-full font-medium transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                  </svg>
                  Request Procedure
                </motion.button>
              </div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
              >
                <div className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6">
                  <div className="flex items-center">
                    <div className="p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]">
                      <FaTooth className="h-6 w-6" />
                    </div>
                    <Link to='/patients'>
                    <div className="ml-4">
                      <h2 className="text-sm font-medium text-gray-500">Total Patients</h2>
                      <p className="text-2xl font-bold text-[#0077B6]">{patients.length}</p>
                    </div>
                    </Link>
                  </div>
                </div>

                <div className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6">
                  <div className="flex items-center">
                    <div className="p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]">
                      <FaCalendarAlt className="h-6 w-6" />
                    </div>
                    <Link to='/calendar'>
                    <div className="ml-4">
                      <h2 className="text-sm font-medium text-gray-500">Today's Appointments</h2>
                      <p className="text-2xl font-bold text-[#0077B6]">
                        {appointments.filter(appt => new Date(appt.date).toDateString() === new Date().toDateString()).length}
                      </p>
                    </div>
                  </Link>
                  </div>
                </div>


                <div className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6">
                  <div className="flex items-center">
                    <div className="p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]">
                      <MdOutlineSupervisorAccount className="h-7 w-7" />
                    </div>
                    <div className="ml-4">
                      <h2 className="text-sm font-medium text-gray-500">Pending Requests</h2>
                      <p className="text-2xl font-bold text-[#0077B6]">
                        {procedureRequests.filter(req => req.status === 'pending').length}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-[rgba(0,119,182,0.1)] p-6">
                  <div className="flex items-center">
                    <div className="p-3 rounded-full bg-[rgba(0,119,182,0.1)] text-[#0077B6]">
                      <MdHealthAndSafety className="h-7 w-7" />
                    </div>
                    <div className="ml-4">
                      <h2 className="text-sm font-medium text-gray-500">Approved Requests</h2>
                      <p className="text-2xl font-bold text-[#0077B6]">
                        {procedureRequests.filter(req => req.status === 'approved').length}
                      </p>
                    </div>
                  </div>
                </div>
              </motion.div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                {/* Appointments Section */}
                <AppointmentsWidget
                  filteredAppointments={filteredAppointments}
                  dayFilter={dayFilter}
                  setDayFilter={setDayFilter}
                  hourFilter={hourFilter}
                  setHourFilter={setHourFilter}
                  handleViewPatient={handleViewPatient}
                  patients={patients}
                />

                {/* Procedure Requests Section */}
                <ProcedureRequestsWidget procedureRequests={procedureRequests} />
              </div>
            </motion.div>
          </div>
        </main>
      </div>

      {/* Success Modal */}
      <SuccessModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        title="Procedure Request Submitted"
        message={successMessage}
      />

      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className="bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto"
          >
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold text-[#0077B6]">Request Procedure</h2>
                <button
                  onClick={() => setShowModal(false)}
                  className="text-gray-400 hover:text-gray-500"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <form onSubmit={handleRequestProcedure} className="space-y-6">
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Procedure Type*</label>
                    <select
                      name="procedureType"
                      value={formData.procedureType}
                      onChange={handleInputChange}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                      required
                    >
                      <option value="">Select Procedure Type</option>
                      <option value="Periodontics">Periodontics</option>
                      <option value="Endodontics">Endodontics</option>
                      <option value="Oral Surgery">Oral Surgery</option>
                      <option value="Fixed Prosthodontics">Fixed Prosthodontics</option>
                      <option value="Removable Prosthodontics">Removable Prosthodontics</option>
                      <option value="Operative">Operative</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Additional Notes</label>
                    <textarea
                      name="notes"
                      value={formData.notes}
                      onChange={handleInputChange}
                      rows="4"
                      placeholder="Provide any additional information about the procedure request"
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    />
                  </div>
                </div>

                <div className="flex justify-end space-x-4 pt-4">
                  <motion.button
                    type="button"
                    onClick={() => setShowModal(false)}
                    className="px-6 py-2 border border-[#20B2AA] text-[#20B2AA] rounded-full hover:bg-[rgba(32,178,170,0.1)] font-medium transition-all duration-300"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    Cancel
                  </motion.button>
                  <motion.button
                    type="submit"
                    className="px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full font-medium transition-all duration-300 shadow-md hover:shadow-lg"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    Submit Request
                  </motion.button>
                </div>
              </form>
            </div>
          </motion.div>
        </div>
      )}

      {selectedPatient && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className="bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto"
          >
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold text-[#0077B6]">{selectedPatient.fullName}</h2>
                <button
                  onClick={() => setSelectedPatient(null)}
                  className="text-gray-400 hover:text-gray-500"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="space-y-6">
                <div className="bg-[rgba(0,119,182,0.05)] p-6 rounded-lg">
                  <h3 className="text-lg font-semibold text-[#0077B6] mb-4 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-[#0077B6]" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                    </svg>
                    Personal Information
                  </h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <h4 className="text-sm font-medium text-gray-500">National ID</h4>
                      <p className="text-sm text-gray-900 mt-1">{selectedPatient.nationalId}</p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-gray-500">Phone</h4>
                      <p className="text-sm text-gray-900 mt-1">{selectedPatient.phoneNumber}</p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-gray-500">Gender</h4>
                      <p className="text-sm text-gray-900 mt-1">{selectedPatient.gender}</p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-gray-500">Age</h4>
                      <p className="text-sm text-gray-900 mt-1">{selectedPatient.age}</p>
                    </div>
                  </div>
                </div>

                <div className="bg-[rgba(0,119,182,0.05)] p-6 rounded-lg">
                  <h3 className="text-lg font-semibold text-[#0077B6] mb-4 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-[#0077B6]" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 00-1-1zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z" clipRule="evenodd" />
                    </svg>
                    Medical History
                  </h3>
                  <div className="space-y-4">
                    <div>
                      <h4 className="text-sm font-medium text-gray-500">Chronic Diseases</h4>
                      <p className="text-sm text-gray-900 mt-1">
                        {selectedPatient.medicalInfo?.chronicDiseases?.join(', ') || 'None'}
                      </p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-gray-500">Recent Surgical Procedures</h4>
                      <p className="text-sm text-gray-900 mt-1">
                        {selectedPatient.medicalInfo?.recentSurgicalProcedures || 'None'}
                      </p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-gray-500">Current Medications</h4>
                      <p className="text-sm text-gray-900 mt-1">
                        {selectedPatient.medicalInfo?.currentMedications || 'None'}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-[rgba(0,119,182,0.05)] p-6 rounded-lg">
                  <h3 className="text-lg font-semibold text-[#0077B6] mb-4 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-[#0077B6]" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                    Chief Complaint
                  </h3>
                  <p className="text-sm text-gray-900">{selectedPatient.medicalInfo?.chiefComplaint || 'None'}</p>
                </div>

                <div className="flex justify-end">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => {
                      const patientId = selectedPatient.nationalId || selectedPatient._id;
                      if (patientId) {
                        navigate(`/patientprofile/${patientId}`);
                      } else {
                        console.error('No valid patient ID found:', selectedPatient);
                      }
                    }}
                    className="px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white rounded-full font-medium transition-all duration-300 shadow-md hover:shadow-lg"
                  >
                    View Full Profile
                  </motion.button>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
};

export default Dashboard;
