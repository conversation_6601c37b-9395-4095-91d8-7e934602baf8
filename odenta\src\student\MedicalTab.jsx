import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import axios from 'axios';
import { motion } from 'framer-motion';
import { FaFileMedical, FaProcedures, FaUser } from 'react-icons/fa';
import { useAuth } from '../context/AuthContext';

const MedicalTab = ({ onSave }) => {
  const { nationalId } = useParams();
  const { token } = useAuth();
  const [patientData, setPatientData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [isEditing, setIsEditing] = useState(false);

  const [formData, setFormData] = useState({
    fullName: '',
    nationalId: '',
    phoneNumber: '',
    gender: '',
    age: '',
    address: '',
    occupation: '',
    medicalInfo: {
      chronicDiseases: [],
      recentSurgicalProcedures: '',
      currentMedications: '',
      chiefComplaint: '',
    },
  });

  useEffect(() => {
    const fetchPatientData = async () => {
      try {
        const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/patients/public/${nationalId}`);
        const patient = response.data;
        console.log('MedicalTab - Fetched patient data:', patient);
        console.log('MedicalTab - Patient medicalInfo:', patient.medicalInfo);
        
        setPatientData(patient);
        setFormData({
          fullName: patient.fullName || '',
          nationalId: patient.nationalId || '',
          phoneNumber: patient.phoneNumber || '',
          gender: patient.gender || '',
          age: patient.age || '',
          address: patient.address || '',
          occupation: patient.occupation || '',
          medicalInfo: {
            chronicDiseases: patient.medicalInfo?.chronicDiseases || [],
            recentSurgicalProcedures: patient.medicalInfo?.recentSurgicalProcedures || '',
            currentMedications: patient.medicalInfo?.currentMedications || '',
            chiefComplaint: patient.medicalInfo?.chiefComplaint || '',
          },
        });
      } catch (err) {
        console.error('Fetch error:', err.response?.status, err.response?.data);
        const message = err.response?.status === 401
          ? 'Unauthorized: Please log in again.'
          : err.response?.data?.message || 'Failed to load patient data';
        setError(message);
      } finally {
        setLoading(false);
      }
    };

    fetchPatientData();
  }, [nationalId, token]);

  const handleEditToggle = () => {
    setIsEditing(!isEditing);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    if (name.startsWith('medical-')) {
      const medicalField = name.replace('medical-', '');
      setFormData(prev => ({
        ...prev,
        medicalInfo: { ...prev.medicalInfo, [medicalField]: value },
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  const handleChronicDiseaseChange = (e) => {
    const { value, checked } = e.target;
    setFormData(prev => {
      const updatedDiseases = checked
        ? [...prev.medicalInfo.chronicDiseases, value]
        : prev.medicalInfo.chronicDiseases.filter(disease => disease !== value);

      return {
        ...prev,
        medicalInfo: { ...prev.medicalInfo, chronicDiseases: updatedDiseases },
      };
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      // Update the patient data with the new info
      const response = await axios.put(
        `${process.env.REACT_APP_API_URL}/api/patients/${nationalId}`,
        {
          fullName: formData.fullName,
          phoneNumber: formData.phoneNumber,
          gender: formData.gender,
          age: formData.age,
          address: formData.address,
          occupation: formData.occupation,
          medicalInfo: formData.medicalInfo,
          drId: patientData.drId // Include drId as required by patientSchema
        },
        { headers: { Authorization: `Bearer ${token}` } }
      );

      if (response.data && response.data.patient) {
        setPatientData(response.data.patient);
        setIsEditing(false);

        // Call the onSave callback if provided
        if (onSave) {
          onSave(formData.medicalInfo);
        }
      } else {
        throw new Error('Invalid patient data received from server');
      }
    } catch (err) {
      console.error('Update error:', err.response?.status, err.response?.data);
      setError(err.response?.data?.message || 'Failed to update patient data');
    }
  };

  if (loading) {
    return (
      <div className="p-4 bg-white rounded-lg shadow-sm">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
        </div>
      </div>
    );
  }

  if (error || !patientData) {
    return (
      <div className="p-4 bg-white rounded-lg shadow-sm">
        <div className="text-red-500">{error || 'Patient data not found'}</div>
      </div>
    );
  }

  const chronicDiseaseOptions = [
    'Diabetes',
    'Hypertension',
    'Heart Disease',
    'Asthma',
    'Thyroid Disorder',
    'Kidney Disease',
    'Liver Disease',
    'Arthritis',
    'Cancer',
    'Other'
  ];

  return (
    <div className="w-full">
      {isEditing ? (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="bg-white rounded-lg p-6 shadow-sm"
        >
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold text-[#0077B6]">Edit Medical Information</h2>
            <button
              onClick={handleEditToggle}
              className="text-gray-500 hover:text-gray-700"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-[#0077B6] mb-4">Personal Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Full Name</label>
                  <input
                    type="text"
                    name="fullName"
                    value={formData.fullName}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">National ID</label>
                  <input
                    type="text"
                    name="nationalId"
                    value={formData.nationalId}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    disabled
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Phone Number</label>
                  <input
                    type="text"
                    name="phoneNumber"
                    value={formData.phoneNumber}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Gender</label>
                  <select
                    name="gender"
                    value={formData.gender}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Select Gender</option>
                    <option value="male">Male</option>
                    <option value="female">Female</option>
                    <option value="other">Other</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Age</label>
                  <input
                    type="number"
                    name="age"
                    value={formData.age}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Address</label>
                  <input
                    type="text"
                    name="address"
                    value={formData.address}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Occupation</label>
                  <input
                    type="text"
                    name="occupation"
                    value={formData.occupation}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-[#0077B6] mb-4">Medical Information</h3>
              <div>
                <h4 className="text-md font-medium text-gray-700 mb-2">Chronic Diseases</h4>
                <div className="grid grid-cols-2 gap-2">
                  {chronicDiseaseOptions.map(disease => (
                    <div key={disease} className="flex items-center">
                      <input
                        type="checkbox"
                        id={`disease-${disease}`}
                        value={disease}
                        checked={formData.medicalInfo.chronicDiseases.includes(disease)}
                        onChange={handleChronicDiseaseChange}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor={`disease-${disease}`} className="ml-2 text-sm text-gray-700">
                        {disease}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              <div className="mt-4">
                <h4 className="text-md font-medium text-gray-700 mb-2">Recent Surgical Procedures</h4>
                <textarea
                  name="medical-recentSurgicalProcedures"
                  value={formData.medicalInfo.recentSurgicalProcedures}
                  onChange={handleInputChange}
                  rows="3"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="List any recent surgical procedures"
                />
              </div>

              <div className="mt-4">
                <h4 className="text-md font-medium text-gray-700 mb-2">Current Medications</h4>
                <textarea
                  name="medical-currentMedications"
                  value={formData.medicalInfo.currentMedications}
                  onChange={handleInputChange}
                  rows="3"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="List any current medications"
                />
              </div>

              <div className="mt-4">
                <h4 className="text-md font-medium text-gray-700 mb-2">Chief Complaint</h4>
                <textarea
                  name="medical-chiefComplaint"
                  value={formData.medicalInfo.chiefComplaint}
                  onChange={handleInputChange}
                  rows="3"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Describe the patient's main complaint or reason for visit"
                  required
                />
              </div>
            </div>

            <div className="flex justify-end space-x-4 pt-4">
              <motion.button
                type="button"
                onClick={handleEditToggle}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 font-medium transition-colors"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Cancel
              </motion.button>
              <motion.button
                type="submit"
                className="px-4 py-2 bg-[#0077B6] text-white rounded-lg hover:bg-blue-700 font-medium transition-colors"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Save Changes
              </motion.button>
            </div>
          </form>
        </motion.div>
      ) : (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="space-y-6"
        >
          <div className="bg-[rgba(0,119,182,0.05)] p-6 rounded-lg border border-[rgba(0,119,182,0.1)]">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold text-[#0077B6] flex items-center">
                <FaUser className="h-5 w-5 mr-2 text-[#0077B6]" />
                Personal Information
              </h2>
              <button
                onClick={handleEditToggle}
                className="px-3 py-1 text-sm bg-[#0077B6] text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Edit
              </button>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="text-sm font-medium text-gray-500">Full Name</h3>
                <p className="mt-1 text-sm text-gray-900">{patientData.fullName}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">National ID</h3>
                <p className="mt-1 text-sm text-gray-900">{patientData.nationalId}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">Phone Number</h3>
                <p className="mt-1 text-sm text-gray-900">{patientData.phoneNumber}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">Gender</h3>
                <p className="mt-1 text-sm text-gray-900">{patientData.gender}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">Age</h3>
                <p className="mt-1 text-sm text-gray-900">{patientData.age}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">Address</h3>
                <p className="mt-1 text-sm text-gray-900">{patientData.address || 'Not provided'}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">Occupation</h3>
                <p className="mt-1 text-sm text-gray-900">{patientData.occupation || 'Not provided'}</p>
              </div>
            </div>
          </div>

          <div className="bg-[rgba(0,119,182,0.05)] p-6 rounded-lg border border-[rgba(0,119,182,0.1)]">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold text-[#0077B6] flex items-center">
                <FaFileMedical className="h-5 w-5 mr-2 text-[#0077B6]" />
                Medical History
              </h2>
            </div>
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-gray-500">Chronic Diseases</h3>
                <p className="mt-1 text-sm text-gray-900">
                  {patientData.medicalInfo?.chronicDiseases?.length > 0
                    ? patientData.medicalInfo.chronicDiseases.join(', ')
                    : 'None'}
                </p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">Recent Surgical Procedures</h3>
                <p className="mt-1 text-sm text-gray-900">
                  {patientData.medicalInfo?.recentSurgicalProcedures || 'None'}
                </p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">Current Medications</h3>
                <p className="mt-1 text-sm text-gray-900">
                  {patientData.medicalInfo?.currentMedications || 'None'}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-[rgba(0,119,182,0.05)] p-6 rounded-lg border border-[rgba(0,119,182,0.1)]">
            <h2 className="text-lg font-semibold text-[#0077B6] mb-4 flex items-center">
              <FaProcedures className="h-5 w-5 mr-2 text-[#0077B6]" />
              Chief Complaint
            </h2>
            <p className="text-sm text-gray-900">
              {patientData.medicalInfo?.chiefComplaint || 'None'}
            </p>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default MedicalTab;
