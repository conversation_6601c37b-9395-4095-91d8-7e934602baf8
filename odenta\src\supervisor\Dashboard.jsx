import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { useAuth } from '../context/AuthContext';
import Navbar from '../student/Navbar';
import Loader from '../components/Loader';
import { motion } from 'framer-motion';
import {
  FaUserMd, FaCalendarAlt, FaStar, FaCheck, FaTimes,
  FaChartPie, FaPercentage, FaStarHalfAlt, FaFilter, FaSearch,
  FaChartBar, FaClipboardCheck, FaUserGraduate, FaSignature,
  FaCalendarCheck, FaListAlt, FaCheckCircle
} from 'react-icons/fa';
import { Chart as ChartJS, ArcElement, Tooltip, Legend, CategoryScale, LinearScale, BarElement, Title } from 'chart.js';
import { Pie, Bar } from 'react-chartjs-2';

// Import custom components
import SignatureManager from './SignatureManager';
import ReviewStepsDisplay from './ReviewStepsDisplay';

// Website color palette
const colorPalette = {
  primary: '#0077B6',
  secondary: '#20B2AA',
  background: '#FFFFFF',
  text: '#333333',
  accent: '#28A745'
};

// Register ChartJS components
ChartJS.register(ArcElement, Tooltip, Legend, CategoryScale, LinearScale, BarElement, Title);

// Chart.js default configuration
ChartJS.defaults.font.family = 'Inter, system-ui, sans-serif';
ChartJS.defaults.font.size = 12;
ChartJS.defaults.color = '#6b7280';
ChartJS.defaults.plugins.tooltip.backgroundColor = 'rgba(0, 0, 0, 0.8)';
ChartJS.defaults.plugins.tooltip.titleColor = '#ffffff';
ChartJS.defaults.plugins.tooltip.bodyColor = '#ffffff';
ChartJS.defaults.plugins.legend.labels.usePointStyle = true;
ChartJS.defaults.plugins.legend.labels.padding = 20;

// Helper function to parse Firestore dates
const parseFirestoreDate = (dateValue) => {
  try {
    if (!dateValue) return null;
    
    // Handle Firestore timestamp objects with toDate method
    if (dateValue && typeof dateValue === 'object' && dateValue.toDate) {
      return dateValue.toDate();
    }
    
    // Handle Firestore timestamp with _seconds
    if (dateValue && typeof dateValue === 'object' && dateValue._seconds) {
      return new Date(dateValue._seconds * 1000);
    }
    
    // Handle regular date strings or Date objects
    if (typeof dateValue === 'string' || dateValue instanceof Date) {
      const date = new Date(dateValue);
      return !isNaN(date.getTime()) ? date : null;
    }
    
    return null;
  } catch (error) {
    console.error('Error parsing date:', error, 'Value:', dateValue);
    return null;
  }
};

// Helper function to format date for display
const formatDateForDisplay = (dateValue) => {
  const date = parseFirestoreDate(dateValue);
  if (!date) return 'N/A';
  return date.toLocaleDateString();
};

// Helper function to format date and time for display
const formatDateTimeForDisplay = (dateValue) => {
  const date = parseFirestoreDate(dateValue);
  if (!date) return 'N/A';
  return date.toLocaleString();
};

const SupervisorDashboard = () => {
  const navigate = useNavigate();
  const { user, token } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [pendingReviews, setPendingReviews] = useState([]);
  const [doneReviews, setDoneReviews] = useState([]);
  const [allReviews, setAllReviews] = useState([]);
  const [selectedReview, setSelectedReview] = useState(null);
  const [dayFilter, setDayFilter] = useState('all');
  const [hourFilter, setHourFilter] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [procedureFilter, setProcedureFilter] = useState('all');
  const [studentFilter, setStudentFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [mainTab, setMainTab] = useState('reviews');
  const [reviewsTab, setReviewsTab] = useState('pending');
  const [savedSignature, setSavedSignature] = useState(null);
  const [showSignatureModal, setShowSignatureModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [analyticsTimeRange, setAnalyticsTimeRange] = useState('month');
  const [analyticsData, setAnalyticsData] = useState({
    statusDistribution: { accepted: 0, pending: 0, denied: 0, total: 0 },
    procedureTypeDistribution: {},
    studentPerformance: {},
    reviewTrends: [],
    qualityMetrics: {
      avgProcedureQuality: 0,
      avgPatientInteraction: 0
    }
  });
  const [uniqueStudents, setUniqueStudents] = useState([]);
  const [uniqueProcedures, setUniqueProcedures] = useState([]);

  const [reviewForm, setReviewForm] = useState({
    procedureQuality: 0,
    patientInteraction: 0,
    comment: '',
    status: 'accepted',
    supervisorSignature: null
  });

  const [stepStatuses, setStepStatuses] = useState({});

  // Initialize step statuses when a review is selected
  useEffect(() => {
    if (selectedReview) {
      const initialStepStatuses = selectedReview.stepStatuses || {};
      console.log('Initializing step statuses for review:', selectedReview.id, initialStepStatuses);
      console.log('Review data:', selectedReview);
      setStepStatuses(initialStepStatuses);
    }
  }, [selectedReview]);

  // Handle step status change
  const handleStepStatusChange = async (stepIndex, status) => {
    console.log(`Step status change requested: Step ${stepIndex} -> ${status}`);
    
    // Update local state immediately for UI responsiveness
    setStepStatuses(prev => {
      const newStatuses = {
        ...prev,
        [stepIndex]: status
      };
      console.log('Updated step statuses:', newStatuses);
      return newStatuses;
    });

    // If we have a selected review, save the step status to the backend immediately
    if (selectedReview && selectedReview.id) {
      try {
        const config = { headers: { Authorization: `Bearer ${token}` } };
        
        // Use the updated step statuses that include the new status
        const updatedStepStatuses = {
          ...stepStatuses,
          [stepIndex]: status
        };

        console.log('Sending step statuses to backend:', updatedStepStatuses);

        // Send only the step statuses update to the backend
        await axios.patch(`${process.env.REACT_APP_API_URL}/api/reviews/${selectedReview.id}/step-statuses`, {
          stepStatuses: updatedStepStatuses
        }, config);

        console.log(`Step ${stepIndex} status updated to: ${status}`);
      } catch (error) {
        console.error('Error updating step status:', error);
        // Revert the local state change if the backend update failed
        setStepStatuses(prev => ({
          ...prev,
          [stepIndex]: prev[stepIndex] // Keep the previous status
        }));
        setError('Failed to update step status. Please try again.');
      }
    }
  };

  // Fetch all reviews and signature data
  useEffect(() => {
    const fetchData = async () => {
      console.log('Auth data:', { user, token }); // Debug auth
      if (!user || !token || user.role !== 'supervisor') {
        setError('Please log in as a supervisor to view this dashboard.');
        navigate('/login');
        return;
      }

      try {
        setLoading(true);
        const config = { headers: { Authorization: `Bearer ${token}` } };

        // Fetch all reviews for the supervisor
        console.log('Fetching all supervisor reviews');
        const allReviewsRes = await axios.get(`${process.env.REACT_APP_API_URL}/api/reviews/supervisor`, config);
        console.log('All reviews response:', allReviewsRes.data);
        console.log('User info:', { id: user.id, university: user.university, role: user.role });

        const reviews = Array.isArray(allReviewsRes.data) ? allReviewsRes.data : [];
        setAllReviews(reviews);

        // Separate reviews by status
        setPendingReviews(reviews.filter(r => r.status === 'pending'));
        setDoneReviews(reviews.filter(r => r.status !== 'pending'));

        // Extract unique students and procedure types for filtering
        const students = [...new Set(reviews.map(r => r.studentId?.name || r.studentName))].filter(Boolean);
        const procedures = [...new Set(reviews.map(r => r.procedureType))].filter(Boolean);

        setUniqueStudents(students);
        setUniqueProcedures(procedures);

        // Fetch supervisor's saved signature if available
        try {
          const signatureRes = await axios.get(`${process.env.REACT_APP_API_URL}/api/supervisors/signature`, config);
          if (signatureRes.data && signatureRes.data.signature) {
            setSavedSignature(signatureRes.data.signature);
          }
        } catch (signatureErr) {
          console.error('Error fetching signature:', signatureErr);
          // Non-critical error, continue without signature
        }

        // Fetch analytics data from the server
        try {
          console.log('Fetching analytics data for time range:', analyticsTimeRange);
          const analyticsRes = await axios.get(`${process.env.REACT_APP_API_URL}/api/reviews/analytics?timeRange=${analyticsTimeRange}`, config);
          console.log('Analytics response:', analyticsRes.data);
          
          if (analyticsRes.data) {
            // Ensure all required fields exist with proper defaults
            const analyticsData = {
              statusDistribution: {
                accepted: analyticsRes.data.statusDistribution?.accepted || 0,
                pending: analyticsRes.data.statusDistribution?.pending || 0,
                denied: analyticsRes.data.statusDistribution?.denied || 0,
                total: (analyticsRes.data.statusDistribution?.accepted || 0) + 
                       (analyticsRes.data.statusDistribution?.pending || 0) + 
                       (analyticsRes.data.statusDistribution?.denied || 0)
              },
              procedureTypeDistribution: analyticsRes.data.procedureTypeDistribution || {},
              studentPerformance: analyticsRes.data.studentPerformance || {},
              reviewTrends: analyticsRes.data.reviewTrends || [],
              qualityMetrics: {
                avgProcedureQuality: analyticsRes.data.qualityMetrics?.avgProcedureQuality || 0,
                avgPatientInteraction: analyticsRes.data.qualityMetrics?.avgPatientInteraction || 0
              }
            };
            setAnalyticsData(analyticsData);
          } else {
            // If server returns no data, calculate from reviews
            calculateAnalytics(reviews);
          }
        } catch (analyticsErr) {
          console.error('Error fetching analytics:', analyticsErr);
          console.log('Falling back to client-side calculation');
          // Always calculate analytics from reviews data
          calculateAnalytics(reviews);
        }

        setLoading(false);
      } catch (err) {
        console.error('Fetch error:', {
          message: err.message,
          status: err.response?.status,
          data: err.response?.data,
          headers: err.response?.headers,
        });
        const errorMessage = err.response?.data?.message ||
                            (err.response?.status === 403 ? 'Access denied: Insufficient permissions' :
                            'Failed to load reviews. Please try again.');
        setError(errorMessage);
        if (err.response?.status === 401) {
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          navigate('/login');
        }
        setPendingReviews([]);
        setDoneReviews([]);
        setAllReviews([]);
        setLoading(false);
      }
    };
    fetchData();
  }, [user, token, navigate, analyticsTimeRange]);

  // Track analytics data changes
  useEffect(() => {
    // Analytics data has been updated
  }, [analyticsData]);

  // Fetch new analytics data when time range changes
  const handleAnalyticsTimeRangeChange = (newRange) => {
    setAnalyticsTimeRange(newRange);
  };

  // Calculate analytics from reviews data
  const calculateAnalytics = (reviews) => {
    if (!Array.isArray(reviews) || reviews.length === 0) {
      return;
    }

    // Status distribution
    const pending = reviews.filter(r => r.status === 'pending').length;
    const accepted = reviews.filter(r => r.status === 'accepted').length;
    const denied = reviews.filter(r => r.status === 'denied').length;

    // Procedure type distribution
    const procedureTypes = {};
    reviews.forEach(review => {
      const type = review.procedureType || 'Unknown';
      if (!procedureTypes[type]) {
        procedureTypes[type] = {
          total: 0,
          accepted: 0,
          denied: 0,
          pending: 0
        };
      }
      procedureTypes[type].total++;
      if (review.status === 'accepted') procedureTypes[type].accepted++;
      else if (review.status === 'denied') procedureTypes[type].denied++;
      else procedureTypes[type].pending++;
    });

    // Student performance
    const studentPerformance = {};
    reviews.forEach(review => {
      const studentName = review.studentName || review.studentId?.name || 'Unknown';
      if (!studentPerformance[studentName]) {
        studentPerformance[studentName] = {
          total: 0,
          accepted: 0,
          denied: 0,
          pending: 0,
          avgProcedureQuality: 0,
          avgPatientInteraction: 0,
          qualityRatings: [],
          interactionRatings: []
        };
      }

      studentPerformance[studentName].total++;
      if (review.status === 'accepted') studentPerformance[studentName].accepted++;
      else if (review.status === 'denied') studentPerformance[studentName].denied++;
      else studentPerformance[studentName].pending++;

      if (review.procedureQuality) {
        studentPerformance[studentName].qualityRatings.push(review.procedureQuality);
      }

      if (review.patientInteraction) {
        studentPerformance[studentName].interactionRatings.push(review.patientInteraction);
      }
    });

    // Calculate averages for each student
    Object.keys(studentPerformance).forEach(student => {
      const { qualityRatings, interactionRatings } = studentPerformance[student];

      if (qualityRatings.length > 0) {
        studentPerformance[student].avgProcedureQuality =
          (qualityRatings.reduce((sum, rating) => sum + rating, 0) / qualityRatings.length).toFixed(1);
      }

      if (interactionRatings.length > 0) {
        studentPerformance[student].avgPatientInteraction =
          (interactionRatings.reduce((sum, rating) => sum + rating, 0) / interactionRatings.length).toFixed(1);
      }

      // Remove the arrays from the response
      delete studentPerformance[student].qualityRatings;
      delete studentPerformance[student].interactionRatings;
    });

    // Review trends by month
    const reviewsByMonth = {};
    const now = new Date();
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(now.getMonth() - 6);

    reviews.forEach(review => {
      const date = parseFirestoreDate(review.submittedDate);
      if (date && date >= sixMonthsAgo) {
        const monthYear = `${date.getMonth() + 1}/${date.getFullYear()}`;
        if (!reviewsByMonth[monthYear]) {
          reviewsByMonth[monthYear] = { total: 0, accepted: 0, denied: 0, pending: 0 };
        }
        reviewsByMonth[monthYear].total++;
        if (review.status === 'accepted') reviewsByMonth[monthYear].accepted++;
        else if (review.status === 'denied') reviewsByMonth[monthYear].denied++;
        else reviewsByMonth[monthYear].pending++;
      }
    });

    // Convert to array and sort by date
    const reviewTrends = Object.keys(reviewsByMonth).map(month => ({
      month,
      ...reviewsByMonth[month]
    })).sort((a, b) => {
      const [aMonth, aYear] = a.month.split('/').map(Number);
      const [bMonth, bYear] = b.month.split('/').map(Number);
      return aYear === bYear ? aMonth - bMonth : aYear - bYear;
    });

    // Quality metrics
    const doneReviewsArr = reviews.filter(r => r.status !== 'pending');
    const avgProcedureQuality = doneReviewsArr.length > 0
      ? (doneReviewsArr.reduce((sum, r) => sum + (r.procedureQuality || 0), 0) / doneReviewsArr.length).toFixed(1)
      : 0;
    const avgPatientInteraction = doneReviewsArr.length > 0
      ? (doneReviewsArr.reduce((sum, r) => sum + (r.patientInteraction || 0), 0) / doneReviewsArr.length).toFixed(1)
      : 0;

    // Update analytics state
    const newAnalyticsData = {
      statusDistribution: { 
        accepted, 
        pending, 
        denied,
        total: accepted + pending + denied
      },
      procedureTypeDistribution: procedureTypes,
      studentPerformance,
      reviewTrends,
      qualityMetrics: {
        avgProcedureQuality,
        avgPatientInteraction
      }
    };

    setAnalyticsData(newAnalyticsData);
  };



  const handleReviewSubmit = async (reviewId) => {
    if (reviewForm.procedureQuality === 0 || reviewForm.patientInteraction === 0) {
      setError('Please provide ratings for Procedure Quality and Patient Interaction');
      return;
    }

    if (!reviewForm.supervisorSignature) {
      setError('Please provide your signature to complete the review');
      return;
    }

    try {
      const config = { headers: { Authorization: `Bearer ${token}` } };
      const updatedReview = {
        status: reviewForm.status,
        procedureQuality: reviewForm.procedureQuality,
        patientInteraction: reviewForm.patientInteraction,
        comment: reviewForm.comment || '',
        supervisorSignature: reviewForm.supervisorSignature,
        stepStatuses: stepStatuses // Include step statuses
      };

      console.log('Submitting review update:', { reviewId, ...updatedReview, supervisorSignature: 'signature data present' });
      console.log('Full request data:', {
        url: `${process.env.REACT_APP_API_URL}/api/reviews/${reviewId}`,
        method: 'PUT',
        data: updatedReview,
        headers: config.headers
      });
      
      const response = await axios.put(`${process.env.REACT_APP_API_URL}/api/reviews/${reviewId}`, updatedReview, config);
      console.log('Review updated:', response.data);

      // Save the signature for future use
      setSavedSignature(reviewForm.supervisorSignature);

      // Update local state
      const reviewToMove = pendingReviews.find(r => r.id === reviewId);

      // Make sure supervisor name is set in the local state
      const updatedReviewWithSupervisor = {
        ...updatedReview,
        supervisorName: user?.name || 'Unknown Supervisor',
        reviewedDate: new Date()
      };

      setPendingReviews(prev => prev.filter(r => r.id !== reviewId));
      setDoneReviews(prev => [...prev, { ...reviewToMove, ...updatedReviewWithSupervisor }]);

      // Recalculate analytics with the updated review
      const allReviewsUpdated = [
        ...doneReviews,
        { ...reviewToMove, ...updatedReviewWithSupervisor },
        ...pendingReviews.filter(r => r.id !== reviewId)
      ];
      calculateAnalytics(allReviewsUpdated);

      // Reset form and close modal
      setSelectedReview(null);
      setReviewForm({
        procedureQuality: 0,
        patientInteraction: 0,
        comment: '',
        status: 'accepted',
        supervisorSignature: null
      });
      setStepStatuses({}); // Reset step statuses
      setError('');

      // Show success message in a styled popup
      setSuccessMessage(`Review ${reviewForm.status === 'accepted' ? 'approved' : 'declined'} successfully!`);
      setShowSuccessModal(true);

      // Auto-hide the success message after 3 seconds
      setTimeout(() => {
        setShowSuccessModal(false);
      }, 3000);
    } catch (err) {
      console.error('Review submit error:', {
        message: err.message,
        status: err.response?.status,
        data: err.response?.data,
        config: err.config
      });
      setError(err.response?.data?.message || `Failed to submit review: ${err.message}`);
    }
  };

  const filterReviews = (reviews) => {
    let filtered = [...reviews];

    // Day filter
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    if (dayFilter === 'today') {
      filtered = filtered.filter(r => {
        const reviewDate = parseFirestoreDate(r.submittedDate);
        return reviewDate && reviewDate.toDateString() === today.toDateString();
      });
    } else if (dayFilter === 'tomorrow') {
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);
      filtered = filtered.filter(r => {
        const reviewDate = parseFirestoreDate(r.submittedDate);
        return reviewDate && reviewDate.toDateString() === tomorrow.toDateString();
      });
    } else if (dayFilter === 'week') {
      const weekEnd = new Date(today);
      weekEnd.setDate(weekEnd.getDate() + 7);
      filtered = filtered.filter(r => {
        const reviewDate = parseFirestoreDate(r.submittedDate);
        return reviewDate && reviewDate >= today && reviewDate <= weekEnd;
      });
    } else if (dayFilter === 'month') {
      const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
      const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, 1);
      filtered = filtered.filter(r => {
        const reviewDate = parseFirestoreDate(r.submittedDate);
        return reviewDate && reviewDate >= monthStart && reviewDate < nextMonth;
      });
    }

    // Hour filter
    if (hourFilter) {
      filtered = filtered.filter(r => {
        const reviewDate = parseFirestoreDate(r.submittedDate);
        return reviewDate && reviewDate.getHours().toString().padStart(2, '0') === hourFilter;
      });
    }

    // Search by patient name or student name
    if (searchQuery) {
      filtered = filtered.filter(r =>
        (r.patientId?.fullName?.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (r.studentName?.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    // Procedure type filter
    if (procedureFilter && procedureFilter !== 'all') {
      filtered = filtered.filter(r => r.procedureType === procedureFilter);
    }

    // Student filter
    if (studentFilter && studentFilter !== 'all') {
      filtered = filtered.filter(r => r.studentName === studentFilter);
    }

    // Status filter (only applies to the "done" tab)
    if (reviewsTab === 'done' && statusFilter && statusFilter !== 'all') {
      filtered = filtered.filter(r => r.status === statusFilter);
    }

    // Sort by date (submittedDate for pending, reviewedDate for done)
    return filtered.sort((a, b) => {
      const dateA = parseFirestoreDate(reviewsTab === 'pending' ? a.submittedDate : a.reviewedDate || a.submittedDate);
      const dateB = parseFirestoreDate(reviewsTab === 'pending' ? b.submittedDate : b.reviewedDate || b.submittedDate);
      if (!dateA && !dateB) return 0;
      if (!dateA) return 1;
      if (!dateB) return -1;
      return dateB.getTime() - dateA.getTime(); // Newest first
    });
  };

  // Analytics calculations
  const totalReviews = pendingReviews.length + doneReviews.length;
  const acceptedReviews = doneReviews.filter(r => r.status === 'accepted').length;
  const deniedReviews = doneReviews.filter(r => r.status === 'denied').length;
  const acceptanceRate = totalReviews > 0 ? ((acceptedReviews / totalReviews) * 100).toFixed(1) : 0;
  const denialRate = totalReviews > 0 ? ((deniedReviews / totalReviews) * 100).toFixed(1) : 0;
  const avgProcedureQuality =
    doneReviews.length > 0
      ? (doneReviews.reduce((sum, r) => sum + (r.procedureQuality || 0), 0) / doneReviews.length).toFixed(1)
      : 0;
  const avgPatientInteraction =
    doneReviews.length > 0
      ? (doneReviews.reduce((sum, r) => sum + (r.patientInteraction || 0), 0) / doneReviews.length).toFixed(1)
      : 0;

  const renderStars = (rating, onClick = null) => (
    <div className="flex">
      {[...Array(5)].map((_, i) => (
        <FaStar
          key={i}
          className={`h-5 w-5 ${i < rating ? 'text-yellow-400' : 'text-gray-300'} ${
            onClick ? 'cursor-pointer' : ''
          }`}
          onClick={onClick ? () => onClick(i + 1) : null}
        />
      ))}
    </div>
  );

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: { staggerChildren: 0.1 },
    },
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 },
  };

  if (loading) {
    return <Loader />;
  }

  return (
    <div className="flex h-screen bg-gray-50">
      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar toggleSidebar={() => {}} />
        <main className="flex-1 overflow-y-auto p-3 sm:p-6" style={{ background: `linear-gradient(to bottom right, ${colorPalette.primary}10, ${colorPalette.background})` }}>
          <div className="max-w-7xl mx-auto">
            {error && (
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-4 sm:mb-6 p-3 sm:p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm"
              >
                <div className="flex items-center">
                  <svg className="w-4 h-4 sm:w-5 sm:h-5 text-red-500 mr-2 sm:mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <p className="text-red-700 font-medium text-sm sm:text-base">{error}</p>
                </div>
              </motion.div>
            )}

            <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.5 }}>
              <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-6 sm:mb-8 gap-3 sm:gap-4">
                <div className="w-full lg:w-auto">
                  <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold mb-1" style={{ color: colorPalette.primary }}>Supervisor Dashboard</h1>
                  <p className="text-sm sm:text-base" style={{ color: colorPalette.text }}>Welcome back, {user?.name || 'Supervisor'}</p>
                </div>
                <div className="flex gap-2 sm:gap-3 w-full lg:w-auto">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setShowSignatureModal(true)}
                    className="px-3 sm:px-4 py-2 text-white rounded-lg flex items-center justify-center text-sm sm:text-base w-full lg:w-auto"
                    style={{ background: `linear-gradient(to right, ${colorPalette.primary}, ${colorPalette.secondary})` }}
                  >
                    <FaSignature className="mr-2" /> Signature
                  </motion.button>
                </div>
              </div>

              {/* Main Tabs */}
              <div className="mb-6 sm:mb-8">
                <div className="flex border-b border-gray-200 overflow-x-auto">
                  <button
                    onClick={() => setMainTab('reviews')}
                    className="px-3 sm:px-6 py-2 sm:py-3 font-medium text-sm sm:text-base whitespace-nowrap"
                    style={{
                      borderBottom: mainTab === 'reviews' ? `2px solid ${colorPalette.primary}` : 'none',
                      color: mainTab === 'reviews' ? colorPalette.primary : '#6b7280',
                      backgroundColor: mainTab === 'reviews' ? `${colorPalette.primary}10` : 'transparent'
                    }}
                  >
                    <FaClipboardCheck className="h-4 w-4 sm:h-5 sm:w-5 inline mr-1 sm:mr-2" />
                    Reviews
                  </button>
                  <button
                    onClick={() => setMainTab('analytics')}
                    className="px-3 sm:px-6 py-2 sm:py-3 font-medium text-sm sm:text-base whitespace-nowrap"
                    style={{
                      borderBottom: mainTab === 'analytics' ? `2px solid ${colorPalette.primary}` : 'none',
                      color: mainTab === 'analytics' ? colorPalette.primary : '#6b7280',
                      backgroundColor: mainTab === 'analytics' ? `${colorPalette.primary}10` : 'transparent'
                    }}
                  >
                    <FaChartBar className="h-4 w-4 sm:h-5 sm:w-5 inline mr-1 sm:mr-2" />
                    Analytics
                  </button>
                </div>
              </div>

              {/* Reviews Section */}
              {mainTab === 'reviews' && (
                <>
                  {/* Analytics Summary */}
                  <motion.div
                    variants={container}
                    initial="hidden"
                    whileInView="show"
                    viewport={{ once: true }}
                    className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6 mb-6 sm:mb-8"
                  >
                    <motion.div
                      variants={item}
                      className="bg-white p-4 sm:p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-blue-200 group"
                    >
                      <div className="flex items-center">
                        <div className="w-10 h-10 sm:w-14 sm:h-14 rounded-lg flex items-center justify-center mr-3 sm:mr-4 transition-colors duration-300"
                          style={{
                            backgroundColor: `${colorPalette.primary}10`,
                            color: colorPalette.primary
                          }}>
                          <FaChartPie className="h-4 w-4 sm:h-6 sm:w-6" style={{ color: colorPalette.primary }} />
                        </div>
                        <div>
                          <p className="text-xs sm:text-sm font-medium" style={{ color: colorPalette.text }}>Total Reviews</p>
                          <p className="text-lg sm:text-2xl font-bold" style={{ color: colorPalette.primary }}>{totalReviews}</p>
                        </div>
                      </div>
                    </motion.div>
                    <motion.div
                      variants={item}
                      className="bg-white p-4 sm:p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-blue-200 group"
                    >
                      <div className="flex items-center">
                        <div className="w-10 h-10 sm:w-14 sm:h-14 rounded-lg flex items-center justify-center mr-3 sm:mr-4 transition-colors duration-300"
                          style={{
                            backgroundColor: `${colorPalette.primary}10`,
                            color: colorPalette.primary
                          }}>
                          <FaPercentage className="h-4 w-4 sm:h-6 sm:w-6" style={{ color: colorPalette.primary }} />
                        </div>
                        <div>
                          <p className="text-xs sm:text-sm font-medium" style={{ color: colorPalette.text }}>Acceptance Rate</p>
                          <p className="text-lg sm:text-2xl font-bold" style={{ color: colorPalette.primary }}>{acceptanceRate}%</p>
                          <p className="text-xs" style={{ color: colorPalette.text }}>Decline: {denialRate}%</p>
                        </div>
                      </div>
                    </motion.div>
                    <motion.div
                      variants={item}
                      className="bg-white p-4 sm:p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-blue-200 group"
                    >
                      <div className="flex items-center">
                        <div className="w-10 h-10 sm:w-14 sm:h-14 rounded-lg flex items-center justify-center mr-3 sm:mr-4 transition-colors duration-300"
                          style={{
                            backgroundColor: `${colorPalette.primary}10`,
                            color: colorPalette.primary
                          }}>
                          <FaStarHalfAlt className="h-4 w-4 sm:h-6 sm:w-6" style={{ color: colorPalette.primary }} />
                        </div>
                        <div>
                          <p className="text-xs sm:text-sm font-medium" style={{ color: colorPalette.text }}>Avg. Procedure Quality</p>
                          <p className="text-lg sm:text-2xl font-bold" style={{ color: colorPalette.primary }}>{avgProcedureQuality}/5</p>
                        </div>
                      </div>
                    </motion.div>
                    <motion.div
                      variants={item}
                      className="bg-white p-4 sm:p-6 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 hover:border-blue-200 group"
                    >
                      <div className="flex items-center">
                        <div className="w-10 h-10 sm:w-14 sm:h-14 rounded-lg flex items-center justify-center mr-3 sm:mr-4 transition-colors duration-300"
                          style={{
                            backgroundColor: `${colorPalette.primary}10`,
                            color: colorPalette.primary
                          }}>
                          <FaStarHalfAlt className="h-4 w-4 sm:h-6 sm:w-6" style={{ color: colorPalette.primary }} />
                        </div>
                        <div>
                          <p className="text-xs sm:text-sm font-medium" style={{ color: colorPalette.text }}>Avg. Patient Interaction</p>
                          <p className="text-lg sm:text-2xl font-bold" style={{ color: colorPalette.primary }}>{avgPatientInteraction}/5</p>
                        </div>
                      </div>
                    </motion.div>
                  </motion.div>

                  {/* Reviews Tabs */}
                  <div className="mb-4 sm:mb-6">
                    <div className="flex border-b border-gray-200 overflow-x-auto">
                      <button
                        onClick={() => setReviewsTab('pending')}
                        className="px-3 sm:px-4 py-2 font-medium text-xs sm:text-sm whitespace-nowrap"
                        style={{
                          borderBottom: reviewsTab === 'pending' ? `2px solid ${colorPalette.primary}` : 'none',
                          color: reviewsTab === 'pending' ? colorPalette.primary : '#6b7280'
                        }}
                      >
                        Pending Reviews ({pendingReviews.length})
                      </button>
                      <button
                        onClick={() => setReviewsTab('done')}
                        className="px-3 sm:px-4 py-2 font-medium text-xs sm:text-sm whitespace-nowrap"
                        style={{
                          borderBottom: reviewsTab === 'done' ? `2px solid ${colorPalette.primary}` : 'none',
                          color: reviewsTab === 'done' ? colorPalette.primary : '#6b7280'
                        }}
                      >
                        Done Reviews ({doneReviews.length})
                      </button>
                    </div>
                  </div>
                </>
              )}

              {/* Analytics Section */}
              {mainTab === 'analytics' && (
                <div className="mb-4 sm:mb-6">
                  <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-4 sm:mb-6 gap-3 sm:gap-4">
                    <h2 className="text-lg sm:text-xl font-bold" style={{ color: colorPalette.primary }}>Analytics Dashboard</h2>
                    <div className="flex gap-2 sm:gap-3 w-full lg:w-auto">
                      <select
                        value={analyticsTimeRange}
                        onChange={(e) => handleAnalyticsTimeRangeChange(e.target.value)}
                        className="px-3 sm:px-4 py-2 border border-gray-300 rounded-lg text-sm w-full lg:w-auto"
                        style={{
                          outline: 'none',
                          boxShadow: 'none',
                          borderColor: '#e5e7eb',
                          ':focus': { borderColor: colorPalette.primary }
                        }}
                      >
                        <option value="week">Last Week</option>
                        <option value="month">Last Month</option>
                        <option value="year">Last Year</option>
                        <option value="all">All Time</option>
                      </select>
                    </div>
                  </div>

                  {/* Analytics Content */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 mb-4 sm:mb-6">
                    {/* Status Distribution Chart */}
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.1 }}
                      className="bg-white p-4 sm:p-6 rounded-xl shadow-sm border border-gray-100"
                    >
                      <h3 className="text-base sm:text-lg font-semibold mb-3 sm:mb-4" style={{ color: colorPalette.primary }}>Review Status Distribution</h3>
                      <div className="h-48 sm:h-64 flex items-center justify-center">
                        {analyticsData.statusDistribution && Object.values(analyticsData.statusDistribution).some(val => val > 0) ? (
                          <Pie
                            data={{
                              labels: ['Accepted', 'Pending', 'Denied'],
                              datasets: [
                                {
                                  data: [
                                    analyticsData.statusDistribution.accepted || 0,
                                    analyticsData.statusDistribution.pending || 0,
                                    analyticsData.statusDistribution.denied || 0
                                  ],
                                  backgroundColor: [
                                    `${colorPalette.accent}B3`,  // accent with opacity
                                    `${colorPalette.primary}B3`, // primary with opacity
                                    'rgba(239, 68, 68, 0.7)'     // red
                                  ],
                                  borderColor: [
                                    colorPalette.accent,
                                    colorPalette.primary,
                                    'rgba(239, 68, 68, 1)'
                                  ],
                                  borderWidth: 1,
                                }
                              ]
                            }}
                            options={{
                              responsive: true,
                              maintainAspectRatio: false,
                              plugins: {
                                legend: {
                                  position: 'bottom',
                                  labels: {
                                    usePointStyle: true,
                                    padding: 15,
                                    font: {
                                      size: 10
                                    }
                                  }
                                },
                                tooltip: {
                                  callbacks: {
                                    label: function(context) {
                                      const label = context.label || '';
                                      const value = context.raw || 0;
                                      const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                      const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
                                      return `${label}: ${value} (${percentage}%)`;
                                    }
                                  }
                                }
                              }
                            }}
                            onRender={() => console.log('Pie chart rendered successfully')}
                            onError={(error) => console.error('Pie chart error:', error)}
                          />
                        ) : (
                          <div className="text-center text-gray-500">
                            <svg className="h-12 w-12 sm:h-16 sm:w-16 mx-auto mb-3 sm:mb-4 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                            </svg>
                            <p className="text-sm">No review data available</p>
                          </div>
                        )}
                      </div>
                    </motion.div>

                    {/* Procedure Type Distribution */}
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.2 }}
                      className="bg-white p-4 sm:p-6 rounded-xl shadow-sm border border-gray-100"
                    >
                      <h3 className="text-base sm:text-lg font-semibold mb-3 sm:mb-4" style={{ color: colorPalette.primary }}>Procedure Type Distribution</h3>
                      <div className="h-48 sm:h-64 flex items-center justify-center">
                        {analyticsData.procedureTypeDistribution && Object.keys(analyticsData.procedureTypeDistribution).length > 0 ? (
                          <Bar
                            data={{
                              labels: Object.keys(analyticsData.procedureTypeDistribution),
                              datasets: [
                                {
                                  label: 'Accepted',
                                  data: Object.values(analyticsData.procedureTypeDistribution).map(p => p.accepted || 0),
                                  backgroundColor: `${colorPalette.accent}B3`,
                                  borderColor: colorPalette.accent,
                                  borderWidth: 1,
                                },
                                {
                                  label: 'Pending',
                                  data: Object.values(analyticsData.procedureTypeDistribution).map(p => p.pending || 0),
                                  backgroundColor: `${colorPalette.primary}B3`,
                                  borderColor: colorPalette.primary,
                                  borderWidth: 1,
                                },
                                {
                                  label: 'Denied',
                                  data: Object.values(analyticsData.procedureTypeDistribution).map(p => p.denied || 0),
                                  backgroundColor: 'rgba(239, 68, 68, 0.7)',
                                  borderColor: 'rgba(239, 68, 68, 1)',
                                  borderWidth: 1,
                                }
                              ]
                            }}
                            options={{
                              responsive: true,
                              maintainAspectRatio: false,
                              scales: {
                                x: {
                                  stacked: true,
                                  ticks: {
                                    maxRotation: 45,
                                    minRotation: 0,
                                    font: {
                                      size: 10
                                    }
                                  }
                                },
                                y: {
                                  stacked: true,
                                  beginAtZero: true,
                                  ticks: {
                                    stepSize: 1,
                                    font: {
                                      size: 10
                                    }
                                  }
                                }
                              },
                              plugins: {
                                legend: {
                                  position: 'bottom',
                                  labels: {
                                    usePointStyle: true,
                                    padding: 15,
                                    font: {
                                      size: 10
                                    }
                                  }
                                },
                                tooltip: {
                                  mode: 'index',
                                  intersect: false
                                }
                              }
                            }}
                          />
                        ) : (
                          <div className="text-center text-gray-500">
                            <svg className="h-12 w-12 sm:h-16 sm:w-16 mx-auto mb-3 sm:mb-4 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                            </svg>
                            <p className="text-sm">No procedure data available</p>
                          </div>
                        )}
                      </div>
                    </motion.div>
                  </div>

                  {/* Additional Analytics Charts */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 mb-4 sm:mb-6">
                    {/* Student Performance Chart */}
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3 }}
                      className="bg-white p-4 sm:p-6 rounded-xl shadow-sm border border-gray-100"
                    >
                      <h3 className="text-base sm:text-lg font-semibold mb-3 sm:mb-4" style={{ color: colorPalette.primary }}>Student Performance</h3>
                      <div className="h-48 sm:h-64 flex items-center justify-center">
                        {analyticsData.studentPerformance && Object.keys(analyticsData.studentPerformance).length > 0 ? (
                          <Bar
                            data={{
                              labels: Object.keys(analyticsData.studentPerformance).slice(0, 8), // Show top 8 students
                              datasets: [
                                {
                                  label: 'Accepted',
                                  data: Object.values(analyticsData.studentPerformance).slice(0, 8).map(s => s.accepted || 0),
                                  backgroundColor: `${colorPalette.accent}B3`,
                                  borderColor: colorPalette.accent,
                                  borderWidth: 1,
                                },
                                {
                                  label: 'Denied',
                                  data: Object.values(analyticsData.studentPerformance).slice(0, 8).map(s => s.denied || 0),
                                  backgroundColor: 'rgba(239, 68, 68, 0.7)',
                                  borderColor: 'rgba(239, 68, 68, 1)',
                                  borderWidth: 1,
                                }
                              ]
                            }}
                            options={{
                              responsive: true,
                              maintainAspectRatio: false,
                              scales: {
                                x: {
                                  stacked: true,
                                  ticks: {
                                    maxRotation: 45,
                                    minRotation: 0,
                                    font: {
                                      size: 10
                                    }
                                  }
                                },
                                y: {
                                  stacked: true,
                                  beginAtZero: true,
                                  ticks: {
                                    stepSize: 1,
                                    font: {
                                      size: 10
                                    }
                                  }
                                }
                              },
                              plugins: {
                                legend: {
                                  position: 'bottom',
                                  labels: {
                                    usePointStyle: true,
                                    padding: 15,
                                    font: {
                                      size: 10
                                    }
                                  }
                                },
                                tooltip: {
                                  mode: 'index',
                                  intersect: false
                                }
                              }
                            }}
                          />
                        ) : (
                          <div className="text-center text-gray-500">
                            <svg className="h-12 w-12 sm:h-16 sm:w-16 mx-auto mb-3 sm:mb-4 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                            </svg>
                            <p className="text-sm">No student performance data available</p>
                          </div>
                        )}
                      </div>
                    </motion.div>

                    {/* Review Trends Over Time */}
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.4 }}
                      className="bg-white p-4 sm:p-6 rounded-xl shadow-sm border border-gray-100"
                    >
                      <h3 className="text-base sm:text-lg font-semibold mb-3 sm:mb-4" style={{ color: colorPalette.primary }}>Review Trends</h3>
                      <div className="h-48 sm:h-64 flex items-center justify-center">
                        {analyticsData.reviewTrends && analyticsData.reviewTrends.length > 0 ? (
                          <Bar
                            data={{
                              labels: analyticsData.reviewTrends.map(trend => trend.month),
                              datasets: [
                                {
                                  label: 'Total Reviews',
                                  data: analyticsData.reviewTrends.map(trend => trend.total),
                                  backgroundColor: `${colorPalette.primary}B3`,
                                  borderColor: colorPalette.primary,
                                  borderWidth: 1,
                                },
                                {
                                  label: 'Accepted',
                                  data: analyticsData.reviewTrends.map(trend => trend.accepted),
                                  backgroundColor: `${colorPalette.accent}B3`,
                                  borderColor: colorPalette.accent,
                                  borderWidth: 1,
                                }
                              ]
                            }}
                            options={{
                              responsive: true,
                              maintainAspectRatio: false,
                              scales: {
                                x: {
                                  ticks: {
                                    font: {
                                      size: 10
                                    }
                                  }
                                },
                                y: {
                                  beginAtZero: true,
                                  ticks: {
                                    stepSize: 1,
                                    font: {
                                      size: 10
                                    }
                                  }
                                }
                              },
                              plugins: {
                                legend: {
                                  position: 'bottom',
                                  labels: {
                                    usePointStyle: true,
                                    padding: 15,
                                    font: {
                                      size: 10
                                    }
                                  }
                                },
                                tooltip: {
                                  mode: 'index',
                                  intersect: false
                                }
                              }
                            }}
                          />
                        ) : (
                          <div className="text-center text-gray-500">
                            <svg className="h-12 w-12 sm:h-16 sm:w-16 mx-auto mb-3 sm:mb-4 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                            </svg>
                            <p className="text-sm">No trend data available</p>
                          </div>
                        )}
                      </div>
                    </motion.div>
                  </div>

                  {/* Quality Metrics Summary */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.5 }}
                    className="bg-white p-4 sm:p-6 rounded-xl shadow-sm border border-gray-100 mb-4 sm:mb-6"
                  >
                    <h3 className="text-base sm:text-lg font-semibold mb-3 sm:mb-4" style={{ color: colorPalette.primary }}>Quality Metrics Summary</h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
                      <div className="text-center p-3 sm:p-4 rounded-lg" style={{ backgroundColor: `${colorPalette.primary}05` }}>
                        <div className="text-2xl sm:text-3xl font-bold mb-1" style={{ color: colorPalette.primary }}>
                          {analyticsData.qualityMetrics.avgProcedureQuality || 0}
                        </div>
                        <div className="text-xs sm:text-sm" style={{ color: colorPalette.text }}>Avg. Procedure Quality</div>
                        <div className="flex justify-center mt-1">
                          {renderStars(analyticsData.qualityMetrics.avgProcedureQuality || 0)}
                        </div>
                      </div>
                      
                      <div className="text-center p-3 sm:p-4 rounded-lg" style={{ backgroundColor: `${colorPalette.accent}05` }}>
                        <div className="text-2xl sm:text-3xl font-bold mb-1" style={{ color: colorPalette.accent }}>
                          {analyticsData.qualityMetrics.avgPatientInteraction || 0}
                        </div>
                        <div className="text-xs sm:text-sm" style={{ color: colorPalette.text }}>Avg. Patient Interaction</div>
                        <div className="flex justify-center mt-1">
                          {renderStars(analyticsData.qualityMetrics.avgPatientInteraction || 0)}
                        </div>
                      </div>
                      
                      <div className="text-center p-3 sm:p-4 rounded-lg" style={{ backgroundColor: `${colorPalette.secondary}05` }}>
                        <div className="text-2xl sm:text-3xl font-bold mb-1" style={{ color: colorPalette.secondary }}>
                          {analyticsData.statusDistribution.accepted || 0}
                        </div>
                        <div className="text-xs sm:text-sm" style={{ color: colorPalette.text }}>Total Accepted</div>
                        <div className="text-xs mt-1" style={{ color: colorPalette.text }}>
                          {analyticsData.statusDistribution.total > 0 
                            ? `${Math.round((analyticsData.statusDistribution.accepted / analyticsData.statusDistribution.total) * 100)}% rate`
                            : '0% rate'
                          }
                        </div>
                      </div>
                      
                      <div className="text-center p-3 sm:p-4 rounded-lg" style={{ backgroundColor: 'rgba(239, 68, 68, 0.05)' }}>
                        <div className="text-2xl sm:text-3xl font-bold mb-1" style={{ color: 'rgba(239, 68, 68, 1)' }}>
                          {analyticsData.statusDistribution.denied || 0}
                        </div>
                        <div className="text-xs sm:text-sm" style={{ color: colorPalette.text }}>Total Denied</div>
                        <div className="text-xs mt-1" style={{ color: colorPalette.text }}>
                          {analyticsData.statusDistribution.total > 0 
                            ? `${Math.round((analyticsData.statusDistribution.denied / analyticsData.statusDistribution.total) * 100)}% rate`
                            : '0% rate'
                          }
                        </div>
                      </div>
                    </div>
                  </motion.div>

                  {/* Student Performance Details */}
                  {analyticsData.studentPerformance && Object.keys(analyticsData.studentPerformance).length > 0 && (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.6 }}
                      className="bg-white p-4 sm:p-6 rounded-xl shadow-sm border border-gray-100"
                    >
                      <h3 className="text-base sm:text-lg font-semibold mb-3 sm:mb-4" style={{ color: colorPalette.primary }}>Student Performance Details</h3>
                      <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead style={{ backgroundColor: `${colorPalette.primary}05` }}>
                            <tr>
                              <th className="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium uppercase tracking-wider" style={{ color: colorPalette.primary }}>Student</th>
                              <th className="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium uppercase tracking-wider" style={{ color: colorPalette.primary }}>Total Reviews</th>
                              <th className="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium uppercase tracking-wider" style={{ color: colorPalette.primary }}>Accepted</th>
                              <th className="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium uppercase tracking-wider" style={{ color: colorPalette.primary }}>Denied</th>
                              <th className="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium uppercase tracking-wider" style={{ color: colorPalette.primary }}>Avg. Quality</th>
                              <th className="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium uppercase tracking-wider" style={{ color: colorPalette.primary }}>Avg. Interaction</th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {Object.entries(analyticsData.studentPerformance).map(([studentName, data]) => (
                              <tr key={studentName} className="hover:bg-gray-50">
                                <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap text-xs sm:text-sm font-medium text-gray-900">{studentName}</td>
                                <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap text-xs sm:text-sm text-gray-500">{data.total}</td>
                                <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap text-xs sm:text-sm text-gray-500">
                                  <span className="px-2 py-1 text-xs font-semibold rounded-full" style={{ backgroundColor: `${colorPalette.accent}20`, color: colorPalette.accent }}>
                                    {data.accepted}
                                  </span>
                                </td>
                                <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap text-xs sm:text-sm text-gray-500">
                                  <span className="px-2 py-1 text-xs font-semibold rounded-full" style={{ backgroundColor: 'rgba(239, 68, 68, 0.2)', color: 'rgba(239, 68, 68, 1)' }}>
                                    {data.denied}
                                  </span>
                                </td>
                                <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap text-xs sm:text-sm text-gray-500">
                                  <div className="flex items-center">
                                    <span className="mr-2">{data.avgProcedureQuality || 0}</span>
                                    <div className="flex">
                                      {[...Array(5)].map((_, i) => (
                                        <FaStar
                                          key={i}
                                          className={`h-3 w-3 ${i < (data.avgProcedureQuality || 0) ? 'text-yellow-400' : 'text-gray-300'}`}
                                        />
                                      ))}
                                    </div>
                                  </div>
                                </td>
                                <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap text-xs sm:text-sm text-gray-500">
                                  <div className="flex items-center">
                                    <span className="mr-2">{data.avgPatientInteraction || 0}</span>
                                    <div className="flex">
                                      {[...Array(5)].map((_, i) => (
                                        <FaStar
                                          key={i}
                                          className={`h-3 w-3 ${i < (data.avgPatientInteraction || 0) ? 'text-yellow-400' : 'text-gray-300'}`}
                                        />
                                      ))}
                                    </div>
                                  </div>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </motion.div>
                  )}
                </div>
              )}

              {/* Reviews Table - Only show when in reviews tab */}
              {mainTab === 'reviews' && (
                <motion.div
                  variants={container}
                  initial="hidden"
                  whileInView="show"
                  viewport={{ once: true }}
                  className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 overflow-hidden"
                >
                  <div className="p-3 sm:p-6">
                    <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-4 sm:mb-6 gap-3 sm:gap-4">
                      <h2 className="text-lg sm:text-xl font-bold" style={{ color: colorPalette.primary }}>
                        {reviewsTab === 'pending' ? 'Pending Reviews' : 'Done Reviews'}
                      </h2>
                      <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 w-full lg:w-auto">
                        <div className="relative">
                          <input
                            type="text"
                            placeholder="Search by patient or student name"
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            className="pl-8 sm:pl-10 pr-3 sm:pr-4 py-2 border border-gray-300 rounded-lg w-full text-sm"
                            style={{
                              outline: 'none',
                              boxShadow: 'none',
                              borderColor: '#e5e7eb',
                              ':focus': { borderColor: colorPalette.primary }
                            }}
                          />
                          <FaSearch className="absolute left-2 sm:left-3 top-2.5 sm:top-3 text-gray-400 text-sm" />
                        </div>

                        {/* Additional filters */}
                        <div className="flex gap-2">
                          <button
                            onClick={() => document.getElementById('filterDropdown').classList.toggle('hidden')}
                            className="px-3 py-2 border border-gray-300 rounded-lg flex items-center text-sm"
                            style={{
                              borderColor: '#e5e7eb',
                              color: colorPalette.text,
                              transition: 'all 0.2s ease'
                            }}
                            onMouseOver={(e) => (e.currentTarget.style.backgroundColor = '#f9fafb')}
                            onMouseOut={(e) => (e.currentTarget.style.backgroundColor = 'transparent')}
                          >
                            <FaFilter className="mr-2 text-gray-500" /> Filters
                          </button>
                        </div>
                      </div>
                    </div>

                    {/* Filter dropdown */}
                    <div id="filterDropdown" className="mb-4 sm:mb-6 p-3 sm:p-4 border border-gray-200 rounded-lg hidden" style={{ backgroundColor: `${colorPalette.primary}05` }}>
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
                        <div>
                          <label className="block text-xs sm:text-sm font-medium mb-1" style={{ color: colorPalette.text }}>Date Range</label>
                          <select
                            value={dayFilter}
                            onChange={(e) => setDayFilter(e.target.value)}
                            className="w-full px-2 sm:px-3 py-2 border border-gray-300 rounded-lg text-sm"
                            style={{
                              outline: 'none',
                              boxShadow: 'none',
                              borderColor: '#e5e7eb',
                              ':focus': { borderColor: colorPalette.primary }
                            }}
                          >
                            <option value="all">All Dates</option>
                            <option value="today">Today</option>
                            <option value="tomorrow">Tomorrow</option>
                            <option value="week">This Week</option>
                            <option value="month">This Month</option>
                          </select>
                        </div>

                        <div>
                          <label className="block text-xs sm:text-sm font-medium mb-1" style={{ color: colorPalette.text }}>Time</label>
                          <select
                            value={hourFilter}
                            onChange={(e) => setHourFilter(e.target.value)}
                            className="w-full px-2 sm:px-3 py-2 border border-gray-300 rounded-lg text-sm"
                            style={{
                              outline: 'none',
                              boxShadow: 'none',
                              borderColor: '#e5e7eb',
                              ':focus': { borderColor: colorPalette.primary }
                            }}
                          >
                            <option value="">All Hours</option>
                            <option value="09:00">09:00 AM</option>
                            <option value="10:00">10:00 AM</option>
                            <option value="11:00">11:00 AM</option>
                            <option value="12:00">12:00 PM</option>
                            <option value="13:00">01:00 PM</option>
                            <option value="14:00">02:00 PM</option>
                            <option value="15:00">03:00 PM</option>
                          </select>
                        </div>

                        <div>
                          <label className="block text-xs sm:text-sm font-medium mb-1" style={{ color: colorPalette.text }}>Procedure Type</label>
                          <select
                            value={procedureFilter}
                            onChange={(e) => setProcedureFilter(e.target.value)}
                            className="w-full px-2 sm:px-3 py-2 border border-gray-300 rounded-lg text-sm"
                            style={{
                              outline: 'none',
                              boxShadow: 'none',
                              borderColor: '#e5e7eb',
                              ':focus': { borderColor: colorPalette.primary }
                            }}
                          >
                            <option value="all">All Procedures</option>
                            {uniqueProcedures.map(procedure => (
                              <option key={procedure} value={procedure}>{procedure}</option>
                            ))}
                          </select>
                        </div>

                        <div>
                          <label className="block text-xs sm:text-sm font-medium mb-1" style={{ color: colorPalette.text }}>Student</label>
                          <select
                            value={studentFilter}
                            onChange={(e) => setStudentFilter(e.target.value)}
                            className="w-full px-2 sm:px-3 py-2 border border-gray-300 rounded-lg text-sm"
                            style={{
                              outline: 'none',
                              boxShadow: 'none',
                              borderColor: '#e5e7eb',
                              ':focus': { borderColor: colorPalette.primary }
                            }}
                          >
                            <option value="all">All Students</option>
                            {uniqueStudents.map(student => (
                              <option key={student} value={student}>{student}</option>
                            ))}
                          </select>
                        </div>

                        {/* Status filter - only show for done reviews */}
                        {reviewsTab === 'done' && (
                          <div>
                            <label className="block text-xs sm:text-sm font-medium mb-1" style={{ color: colorPalette.text }}>Status</label>
                            <select
                              value={statusFilter}
                              onChange={(e) => setStatusFilter(e.target.value)}
                              className="w-full px-2 sm:px-3 py-2 border border-gray-300 rounded-lg text-sm"
                              style={{
                                outline: 'none',
                                boxShadow: 'none',
                                borderColor: '#e5e7eb',
                                ':focus': { borderColor: colorPalette.primary }
                              }}
                            >
                              <option value="all">All Statuses</option>
                              <option value="accepted">Accepted</option>
                              <option value="denied">Denied</option>
                            </select>
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead style={{ backgroundColor: `${colorPalette.primary}05` }}>
                          <tr>
                            <th className="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium uppercase tracking-wider" style={{ color: colorPalette.primary }}>Date</th>
                            <th className="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium uppercase tracking-wider" style={{ color: colorPalette.primary }}>Patient</th>
                            <th className="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium uppercase tracking-wider" style={{ color: colorPalette.primary }}>Student</th>
                            <th className="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium uppercase tracking-wider" style={{ color: colorPalette.primary }}>Procedure</th>
                            {reviewsTab === 'done' && (
                              <>
                                <th className="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium uppercase tracking-wider" style={{ color: colorPalette.primary }}>Status</th>
                                <th className="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium uppercase tracking-wider" style={{ color: colorPalette.primary }}>Supervisor</th>
                              </>
                            )}
                            {reviewsTab === 'pending' && (
                              <th className="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium uppercase tracking-wider" style={{ color: colorPalette.primary }}>Actions</th>
                            )}
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {filterReviews(reviewsTab === 'pending' ? pendingReviews : doneReviews).length === 0 ? (
                            <tr>
                              <td colSpan={reviewsTab === 'pending' ? 5 : 6} className="px-3 sm:px-6 py-6 sm:py-8 text-center">
                                <div className="flex flex-col items-center justify-center">
                                  <svg className="h-8 w-8 sm:h-12 sm:w-12 text-gray-400 mb-3 sm:mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                  </svg>
                                  <h3 className="text-base sm:text-lg font-medium text-gray-900">
                                    No {reviewsTab === 'pending' ? 'pending' : 'completed'} reviews
                                  </h3>
                                  <p className="mt-1 text-sm text-gray-500 text-center">
                                    {error
                                      ? 'Failed to load reviews due to an error. Check your permissions or try logging in again.'
                                      : reviewsTab === 'pending'
                                        ? 'No pending reviews are available. Check back later or ensure student reviews have been submitted.'
                                        : 'No completed reviews found. Try adjusting the filters or review pending submissions.'}
                                  </p>
                                </div>
                              </td>
                            </tr>
                          ) : (
                            filterReviews(reviewsTab === 'pending' ? pendingReviews : doneReviews).map((review) => (
                              <motion.tr
                                key={review.id}
                                initial={{ opacity: 0 }}
                                animate={{ opacity: 1 }}
                                className="hover:bg-gray-50 cursor-pointer"
                                onClick={() => {
                                  setSelectedReview(review);
                                }}
                              >
                                <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap text-xs sm:text-sm font-medium text-gray-900">
                                  {formatDateForDisplay(reviewsTab === 'pending' ? review.submittedDate : review.reviewedDate || review.submittedDate)}
                                </td>
                                <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap text-xs sm:text-sm text-gray-500">{review.patientId?.fullName || 'N/A'}</td>
                                <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap text-xs sm:text-sm text-gray-500">{review.studentName || 'N/A'}</td>
                                <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap text-xs sm:text-sm text-gray-500">{review.procedureType || 'N/A'}</td>
                                {reviewsTab === 'done' && (
                                  <>
                                    <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap">
                                      <span
                                        className="px-2 sm:px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full"
                                        style={{
                                          backgroundColor: review.status === 'accepted' ? `${colorPalette.accent}20` : '#fee2e2',
                                          color: review.status === 'accepted' ? colorPalette.accent : '#b91c1c'
                                        }}
                                      >
                                        {review.status}
                                      </span>
                                    </td>
                                    <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap text-xs sm:text-sm text-gray-500">{review.supervisorName || 'N/A'}</td>
                                  </>
                                )}
                                {reviewsTab === 'pending' && (
                                  <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap text-xs sm:text-sm">
                                    <button
                                      onClick={() => {
                                        setSelectedReview(review);
                                      }}
                                      style={{ color: colorPalette.primary }}
                                      className="hover:underline text-xs sm:text-sm"
                                    >
                                      Review
                                    </button>
                                  </td>
                                )}
                              </motion.tr>
                            ))
                          )}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </motion.div>
              )}
            </motion.div>
          </div>
        </main>
      </div>

      {selectedReview && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-2 sm:p-4">
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className="bg-white rounded-2xl shadow-2xl w-full max-w-3xl max-h-[90vh] overflow-y-auto"
          >
            <div className="p-3 sm:p-6">
              <div className="flex justify-between items-center mb-4 sm:mb-6">
                <h2 className="text-xl sm:text-2xl font-bold" style={{ color: colorPalette.primary }}>Review Details</h2>
                <button onClick={() => {
                  setSelectedReview(null);
                  setStepStatuses({});
                }} className="text-gray-400 hover:text-gray-500">
                  <svg className="h-5 w-5 sm:h-6 sm:w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="space-y-4 sm:space-y-6">
                {/* Patient Information */}
                <div className="p-3 sm:p-6 rounded-lg" style={{ backgroundColor: `${colorPalette.primary}10` }}>
                  <h3 className="text-base sm:text-lg font-semibold mb-3 sm:mb-4 flex items-center" style={{ color: colorPalette.primary }}>
                    <FaUserMd className="h-4 w-4 sm:h-5 sm:w-5 mr-2" style={{ color: colorPalette.primary }} />
                    Patient Information
                  </h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                    <div>
                      <h4 className="text-xs sm:text-sm font-medium text-gray-500">Name</h4>
                      <p className="text-xs sm:text-sm text-gray-900 mt-1">{selectedReview.patientId?.fullName || 'N/A'}</p>
                    </div>
                    <div>
                      <h4 className="text-xs sm:text-sm font-medium text-gray-500">National ID</h4>
                      <p className="text-xs sm:text-sm text-gray-900 mt-1">{selectedReview.patientId?.nationalId || 'N/A'}</p>
                    </div>
                  </div>
                </div>

                {/* Review Details */}
                <div className="p-3 sm:p-6 rounded-lg" style={{ backgroundColor: `${colorPalette.primary}10` }}>
                  <h3 className="text-base sm:text-lg font-semibold mb-3 sm:mb-4 flex items-center" style={{ color: colorPalette.primary }}>
                    <FaCalendarAlt className="h-4 w-4 sm:h-5 sm:w-5 mr-2" style={{ color: colorPalette.primary }} />
                    Review Details
                  </h3>
                  <div className="space-y-3 sm:space-y-4">
                    <div>
                      <h4 className="text-xs sm:text-sm font-medium text-gray-500">Student</h4>
                      <p className="text-xs sm:text-sm text-gray-900 mt-1">{selectedReview.studentName || 'N/A'}</p>
                    </div>
                    <div>
                      <h4 className="text-xs sm:text-sm font-medium text-gray-500">Student ID</h4>
                      <p className="text-xs sm:text-sm text-gray-900 mt-1">{selectedReview.studentId || 'N/A'}</p>
                    </div>
                    <div>
                      <h4 className="text-xs sm:text-sm font-medium text-gray-500">Procedure</h4>
                      <p className="text-xs sm:text-sm text-gray-900 mt-1">{selectedReview.procedureType || 'N/A'}</p>
                    </div>
                    <div>
                      <h4 className="text-xs sm:text-sm font-medium text-gray-500">Submission Date</h4>
                      <p className="text-xs sm:text-sm text-gray-900 mt-1">
                        {formatDateTimeForDisplay(selectedReview.submittedDate)}
                      </p>
                    </div>
                    <div>
                      <h4 className="text-xs sm:text-sm font-medium text-gray-500">Student Comment</h4>
                      <p className="text-xs sm:text-sm text-gray-900 mt-1">{selectedReview.comment || 'No comment'}</p>
                    </div>
                  </div>
                </div>

                {/* Review Steps */}
                <div className="p-3 sm:p-6 rounded-lg" style={{ backgroundColor: `${colorPalette.primary}10` }}>
                  <h3 className="text-base sm:text-lg font-semibold mb-3 sm:mb-4 flex items-center" style={{ color: colorPalette.primary }}>
                    <FaListAlt className="h-4 w-4 sm:h-5 sm:w-5 mr-2" style={{ color: colorPalette.primary }} />
                    Procedure Steps
                  </h3>
                  <ReviewStepsDisplay
                    reviewSteps={selectedReview.reviewSteps || []}
                    procedureType={selectedReview.procedureType}
                    onStepStatusChange={handleStepStatusChange}
                    isSupervisorMode={true}
                    stepStatuses={stepStatuses}
                  />
                  {console.log('Dashboard: Passing stepStatuses to ReviewStepsDisplay:', stepStatuses)}
                </div>

                {/* Supervisor Review Form (for pending reviews) */}
                {selectedReview.status === 'pending' && (
                  <div className="p-3 sm:p-6 rounded-lg" style={{ backgroundColor: `${colorPalette.primary}10` }}>
                    <h3 className="text-base sm:text-lg font-semibold mb-3 sm:mb-4" style={{ color: colorPalette.primary }}>Submit Review</h3>
                    <div className="space-y-3 sm:space-y-4">
                      <div>
                        <h4 className="text-xs sm:text-sm font-medium text-gray-500 mb-2">Procedure Quality</h4>
                        {renderStars(reviewForm.procedureQuality, (rating) =>
                          setReviewForm({ ...reviewForm, procedureQuality: rating })
                        )}
                      </div>
                      <div>
                        <h4 className="text-xs sm:text-sm font-medium text-gray-500 mb-2">Patient Interaction</h4>
                        {renderStars(reviewForm.patientInteraction, (rating) =>
                          setReviewForm({ ...reviewForm, patientInteraction: rating })
                        )}
                      </div>
                      <div>
                        <h4 className="text-xs sm:text-sm font-medium text-gray-500 mb-2">Supervisor Comment</h4>
                        <textarea
                          value={reviewForm.comment}
                          onChange={(e) => setReviewForm({ ...reviewForm, comment: e.target.value })}
                          className="w-full px-3 sm:px-4 py-2 border border-gray-300 rounded-lg text-sm"
                          style={{
                            outline: 'none',
                            boxShadow: 'none',
                            borderColor: '#e5e7eb',
                            ':focus': { borderColor: colorPalette.primary }
                          }}
                          rows="3"
                          placeholder="Add any comments or feedback"
                        />
                      </div>
                      <div>
                        <h4 className="text-xs sm:text-sm font-medium text-gray-500 mb-2">Review Decision</h4>
                        <div className="flex flex-col sm:flex-row gap-2 sm:gap-4 mt-2">
                          <button
                            type="button"
                            onClick={() => setReviewForm({ ...reviewForm, status: 'accepted' })}
                            className="flex-1 py-2 sm:py-3 px-3 sm:px-4 rounded-lg flex items-center justify-center text-sm"
                            style={{
                              backgroundColor: reviewForm.status === 'accepted'
                                ? colorPalette.accent
                                : '#f3f4f6',
                              color: reviewForm.status === 'accepted'
                                ? '#ffffff'
                                : colorPalette.text,
                              fontWeight: reviewForm.status === 'accepted' ? '500' : 'normal',
                              border: reviewForm.status === 'accepted'
                                ? `2px solid ${colorPalette.accent}`
                                : 'none'
                            }}
                          >
                            <FaCheck className="mr-2" style={{
                              color: reviewForm.status === 'accepted' ? '#ffffff' : colorPalette.accent
                            }} />
                            Accept
                          </button>
                          <button
                            type="button"
                            onClick={() => setReviewForm({ ...reviewForm, status: 'denied' })}
                            className="flex-1 py-2 sm:py-3 px-3 sm:px-4 rounded-lg flex items-center justify-center text-sm"
                            style={{
                              backgroundColor: reviewForm.status === 'denied'
                                ? '#ef4444'
                                : '#f3f4f6',
                              color: reviewForm.status === 'denied'
                                ? '#ffffff'
                                : colorPalette.text,
                              fontWeight: reviewForm.status === 'denied' ? '500' : 'normal',
                              border: reviewForm.status === 'denied'
                                ? '2px solid #dc2626'
                                : 'none'
                            }}
                          >
                            <FaTimes className="mr-2" style={{
                              color: reviewForm.status === 'denied' ? '#ffffff' : '#ef4444'
                            }} />
                            Decline
                          </button>
                        </div>
                      </div>

                      {/* Signature Section */}
                      <div className="mt-4 sm:mt-6 border-t pt-3 sm:pt-4 border-gray-200">
                        <h4 className="text-xs sm:text-sm font-medium text-gray-500 mb-2">Supervisor Signature</h4>

                        {savedSignature ? (
                          <div className="mb-3 sm:mb-4">
                            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-2 sm:mb-3 gap-2">
                              <p className="text-xs sm:text-sm text-gray-600">You have a saved signature</p>
                              <motion.button
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                onClick={() => setReviewForm(prev => ({ ...prev, supervisorSignature: savedSignature }))}
                                className="px-3 sm:px-4 py-2 text-white rounded-lg flex items-center justify-center text-xs sm:text-sm w-full sm:w-auto"
                                style={{ background: `linear-gradient(to right, ${colorPalette.primary}, ${colorPalette.secondary})` }}
                              >
                                <FaSignature className="mr-2" /> Sign
                              </motion.button>
                            </div>

                            {reviewForm.supervisorSignature && (
                              <div className="border rounded-lg p-3 sm:p-4"
                                style={{
                                  borderColor: '#e5e7eb',
                                  backgroundColor: `${colorPalette.primary}05`
                                }}>
                                <h4 className="text-xs sm:text-sm font-medium mb-2" style={{ color: colorPalette.primary }}>Signature Preview</h4>
                                {reviewForm.supervisorSignature.startsWith('data:image') ? (
                                  <img src={reviewForm.supervisorSignature} alt="Signature" className="max-h-12 sm:max-h-16 mx-auto" />
                                ) : (
                                  <p className="font-signature text-sm sm:text-lg text-center">{reviewForm.supervisorSignature}</p>
                                )}
                              </div>
                            )}
                          </div>
                        ) : (
                          <div className="p-3 sm:p-4 rounded-lg mb-3 sm:mb-4"
                            style={{
                              backgroundColor: `${colorPalette.secondary}10`,
                              border: `1px solid ${colorPalette.secondary}30`
                            }}>
                            <p className="text-xs sm:text-sm mb-2" style={{ color: colorPalette.secondary }}>
                              You don't have a saved signature. Please create one first.
                            </p>
                            <motion.button
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                              onClick={() => setShowSignatureModal(true)}
                              className="px-3 sm:px-4 py-2 text-white rounded-lg flex items-center justify-center text-xs sm:text-sm w-full sm:w-auto"
                              style={{ backgroundColor: colorPalette.secondary }}
                            >
                              <FaSignature className="mr-2" /> Create Signature
                            </motion.button>
                          </div>
                        )}
                      </div>

                      <div className="flex justify-end gap-2 sm:gap-4 mt-4 sm:mt-6">
                        <motion.button
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={() => handleReviewSubmit(selectedReview.id)}
                          disabled={reviewForm.procedureQuality === 0 || reviewForm.patientInteraction === 0 || !reviewForm.supervisorSignature}
                          className="px-4 sm:px-6 py-2 sm:py-3 rounded-lg flex items-center justify-center shadow-md text-sm w-full sm:w-auto"
                          style={{
                            backgroundColor: reviewForm.procedureQuality === 0 || reviewForm.patientInteraction === 0 || !reviewForm.supervisorSignature
                              ? '#d1d5db'
                              : 'transparent',
                            background: reviewForm.procedureQuality === 0 || reviewForm.patientInteraction === 0 || !reviewForm.supervisorSignature
                              ? '#d1d5db'
                              : reviewForm.status === 'accepted'
                                ? `linear-gradient(to right, ${colorPalette.accent}, ${colorPalette.accent})`
                                : 'linear-gradient(to right, #ef4444, #b91c1c)',
                            color: reviewForm.procedureQuality === 0 || reviewForm.patientInteraction === 0 || !reviewForm.supervisorSignature
                              ? '#6b7280'
                              : '#ffffff',
                            cursor: reviewForm.procedureQuality === 0 || reviewForm.patientInteraction === 0 || !reviewForm.supervisorSignature
                              ? 'not-allowed'
                              : 'pointer'
                          }}
                        >
                          {reviewForm.status === 'accepted' ? (
                            <FaCheck className="h-4 w-4 sm:h-5 sm:w-5 mr-2" />
                          ) : (
                            <FaTimes className="h-4 w-4 sm:h-5 sm:w-5 mr-2" />
                          )}
                          Submit {reviewForm.status === 'accepted' ? 'Approval' : 'Decline'}
                        </motion.button>
                      </div>
                    </div>
                  </div>
                )}

                {/* Display Supervisor Review (for done reviews) */}
                {selectedReview.status !== 'pending' && (
                  <div className="p-3 sm:p-6 rounded-lg" style={{ backgroundColor: `${colorPalette.primary}10` }}>
                    <h3 className="text-base sm:text-lg font-semibold mb-3 sm:mb-4" style={{ color: colorPalette.primary }}>Supervisor Review</h3>
                    <div className="space-y-3 sm:space-y-4">
                      <div>
                        <h4 className="text-xs sm:text-sm font-medium text-gray-500">Supervisor</h4>
                        <p className="text-xs sm:text-sm text-gray-900 mt-1">{selectedReview.supervisorName || 'N/A'}</p>
                      </div>
                      <div>
                        <h4 className="text-xs sm:text-sm font-medium text-gray-500">Reviewed Date</h4>
                        <p className="text-xs sm:text-sm text-gray-900 mt-1">
                          {formatDateTimeForDisplay(selectedReview.reviewedDate)}
                        </p>
                      </div>
                      <div>
                        <h4 className="text-xs sm:text-sm font-medium text-gray-500">Procedure Quality</h4>
                        {renderStars(selectedReview.procedureQuality || 0)}
                      </div>
                      <div>
                        <h4 className="text-xs sm:text-sm font-medium text-gray-500">Patient Interaction</h4>
                        {renderStars(selectedReview.patientInteraction || 0)}
                      </div>
                      <div>
                        <h4 className="text-xs sm:text-sm font-medium text-gray-500">Comment</h4>
                        <p className="text-xs sm:text-sm text-gray-900 mt-1">{selectedReview.comment || 'No comment'}</p>
                      </div>
                      <div>
                        <h4 className="text-xs sm:text-sm font-medium text-gray-500">Status</h4>
                        <span
                          className="px-2 sm:px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full"
                          style={{
                            backgroundColor: selectedReview.status === 'accepted' ? `${colorPalette.accent}20` : '#fee2e2',
                            color: selectedReview.status === 'accepted' ? colorPalette.accent : '#b91c1c'
                          }}
                        >
                          {selectedReview.status}
                        </span>
                      </div>

                      {/* Signature Display */}
                      <div className="mt-3 sm:mt-4 pt-3 sm:pt-4 border-t border-gray-200">
                        <h4 className="text-xs sm:text-sm font-medium text-gray-500 mb-2">Supervisor Signature</h4>
                        {selectedReview.supervisorSignature ? (
                          <div className="border rounded-lg p-3 sm:p-4" style={{
                            borderColor: '#e5e7eb',
                            backgroundColor: colorPalette.background
                          }}>
                            {selectedReview.supervisorSignature.startsWith('data:image') ? (
                              <img src={selectedReview.supervisorSignature} alt="Signature" className="max-h-16 sm:max-h-20" />
                            ) : (
                              <p className="font-signature text-sm sm:text-lg">{selectedReview.supervisorSignature}</p>
                            )}
                          </div>
                        ) : (
                          <p className="text-xs sm:text-sm text-gray-500">No signature provided</p>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        </div>
      )}

      {/* Signature Modal */}
      {showSignatureModal && (
        <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-2 sm:p-4 backdrop-blur-sm">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
            className="bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
          >
            <div className="p-4 sm:p-6 rounded-t-xl" style={{
              background: `linear-gradient(to right, ${colorPalette.primary}, ${colorPalette.secondary})`
            }}>
              <div className="flex justify-between items-center">
                <h2 className="text-lg sm:text-xl font-bold text-white flex items-center">
                  <FaSignature className="mr-2 sm:mr-3 h-4 w-4 sm:h-5 sm:w-5" />
                  Manage Your Signature
                </h2>
                <button
                  onClick={() => setShowSignatureModal(false)}
                  className="text-white hover:text-blue-200 transition-colors"
                >
                  <svg className="w-5 h-5 sm:w-6 sm:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>

            <div className="p-4 sm:p-6">
              <div className="p-3 sm:p-4 rounded-lg mb-4 sm:mb-6 border-l-4"
                style={{
                  backgroundColor: `${colorPalette.primary}10`,
                  borderColor: colorPalette.primary
                }}>
                <p className="text-xs sm:text-sm" style={{ color: colorPalette.primary }}>
                  Create or update your signature below. This signature will be used for all your review approvals.
                </p>
              </div>

              <SignatureManager
                initialSignature={savedSignature}
                onSignatureSelect={(signature) => {
                  setSavedSignature(signature);
                  // Close modal after a short delay to show success
                  setTimeout(() => setShowSignatureModal(false), 1500);
                }}
              />
            </div>
          </motion.div>
        </div>
      )}

      {/* Success Modal */}
      {showSuccessModal && (
        <motion.div
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 50 }}
          className="fixed top-0 inset-x-0 flex justify-center items-start z-50 pt-4 sm:pt-6 px-2 sm:px-4"
        >
          <div className="bg-white rounded-lg shadow-xl border-l-4 p-3 sm:p-4 flex items-center max-w-md w-full"
            style={{ borderColor: colorPalette.accent }}>
            <div className="p-2 rounded-full mr-3 sm:mr-4" style={{ backgroundColor: `${colorPalette.accent}20` }}>
              <FaCheck className="text-lg sm:text-xl" style={{ color: colorPalette.accent }} />
            </div>
            <div className="flex-1">
              <h3 className="font-medium text-sm sm:text-base" style={{ color: colorPalette.text }}>Success!</h3>
              <p className="text-xs sm:text-sm" style={{ color: '#6b7280' }}>{successMessage}</p>
            </div>
            <button
              onClick={() => setShowSuccessModal(false)}
              className="text-gray-400 hover:text-gray-500"
            >
              <FaTimes className="text-sm sm:text-base" />
            </button>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default SupervisorDashboard;