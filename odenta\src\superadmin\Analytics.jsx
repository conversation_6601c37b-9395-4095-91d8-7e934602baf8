import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { motion } from 'framer-motion';
import { Bar, Pie, Line, Doughnut } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, ArcElement, LineElement, PointElement, Title, Tooltip, Legend } from 'chart.js';
import Navbar from '../student/Navbar';
import Loader from '../components/Loader';
import { useAuth } from '../context/AuthContext';
import SuperAdminSidebar from './SuperAdminSidebar';
import { FaChartBar, FaUserFriends, FaCalendarAlt, FaHospital, FaUserInjured, FaClipboardList, FaCogs, FaShieldAlt, FaUsers, FaGraduationCap } from 'react-icons/fa';

// Register ChartJS components
ChartJS.register(CategoryScale, LinearScale, BarElement, ArcElement, LineElement, PointElement, Title, Tooltip, Legend);

const Analytics = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [analytics, setAnalytics] = useState({
    counts: {
      totalStudents: 0,
      totalSupervisors: 0,
      totalAdmins: 0,
      totalAssistants: 0,
      totalDentists: 0,
      totalUniversities: 0,
      totalPatients: 0,
      totalAppointments: 0,
      totalAccounts: 0
    },
    growth: {
      studentGrowth: [],
      supervisorGrowth: [],
      adminGrowth: [],
      assistantGrowth: [],
      dentistGrowth: [],
      universityGrowth: []
    },
    appointmentStats: {
      statusCounts: {},
      appointmentsByMonth: []
    },
    universityDistribution: [],
    patientDemographics: {
      genderDistribution: [],
      ageDistribution: []
    },
    procedureTypes: [],
    recentActivity: [],
    businessMetrics: {
      activeUsers: { total: 0, byRole: {} },
      universityAdoption: { total: 0, active: 0, adoptionRate: 0 },
      growthRate: { monthly: 0, quarterly: 0 },
      retentionMetrics: { userRetention: 0, universityRetention: 0 }
    },
    systemUsage: {
      appointmentMetrics: { completionRate: 0, cancellationRate: 0 },
      patientMetrics: { averagePatientsPerStudent: 0, treatmentCompletionRate: 0 },
      featureUsage: { mostUsedFeatures: [], leastUsedFeatures: [] }
    },
    performanceMetrics: {
      responseTime: 0,
      uptime: 0,
      errorRate: 0,
      dataQuality: { completeness: 0, accuracy: 0 }
    }
  });

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [timeRange, setTimeRange] = useState('6months'); // '1month', '3months', '6months', '1year'
  const [activeTab, setActiveTab] = useState('overview'); // 'overview', 'business', 'accounts', 'universities', 'patients', 'appointments', 'performance', 'activity'
  const [chartType, setChartType] = useState({
    accounts: 'bar',
    appointments: 'line',
    universities: 'bar',
    gender: 'pie',
    age: 'bar',
    procedures: 'bar'
  });

  const navigate = useNavigate();
  const { user, token } = useAuth();

  useEffect(() => {
    const fetchAnalytics = async () => {
      if (!user || !token) {
        setError('Please log in to view analytics.');
        setLoading(false);
        return;
      }

      setLoading(true);
      try {
        const config = {
          headers: { Authorization: `Bearer ${token}` },
          params: { timeRange }
        };
        const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/superadmin/analytics`, config);
        setAnalytics(response.data);
      } catch (err) {
        console.error('Fetch error:', err.response?.data || err.message);
        const errorMessage = err.response?.status === 401
          ? 'Unauthorized. Please log in again.'
          : err.response?.data?.message || 'Failed to load analytics';
        setError(errorMessage);
        if (err.response?.status === 401) {
          navigate('/login');
        }
      } finally {
        setLoading(false);
      }
    };
    fetchAnalytics();
  }, [user, token, navigate, timeRange]);

  // Animation variants with reduced motion
  const item = {
    hidden: { opacity: 0, y: 5 },
    show: { opacity: 1, y: 0, transition: { duration: 0.15 } },
  };

  // Toggle chart type
  const toggleChartType = (chartName) => {
    setChartType(prev => {
      const types = {
        'bar': 'line',
        'line': 'pie',
        'pie': 'doughnut',
        'doughnut': 'bar'
      };
      return { ...prev, [chartName]: types[prev[chartName]] };
    });
  };

  // Chart Data
  const accountsData = {
    labels: ['Students', 'Supervisors', 'Admins', 'Assistants', 'Dentists'],
    datasets: [
      {
        label: 'Number of Accounts',
        data: [
          analytics.counts.totalStudents,
          analytics.counts.totalSupervisors,
          analytics.counts.totalAdmins,
          analytics.counts.totalAssistants,
          analytics.counts.totalDentists
        ],
        backgroundColor: [
          'rgba(54, 162, 235, 0.7)',
          'rgba(255, 99, 132, 0.7)',
          'rgba(255, 206, 86, 0.7)',
          'rgba(75, 192, 192, 0.7)',
          'rgba(153, 102, 255, 0.7)'
        ],
        borderColor: [
          'rgba(54, 162, 235, 1)',
          'rgba(255, 99, 132, 1)',
          'rgba(255, 206, 86, 1)',
          'rgba(75, 192, 192, 1)',
          'rgba(153, 102, 255, 1)'
        ],
        borderWidth: 1,
      },
    ],
  };

  const appointmentsData = {
    labels: analytics.appointmentStats.appointmentsByMonth.map(data => `${data.month} ${data.year}`) || ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    datasets: [
      {
        label: 'Appointments',
        data: analytics.appointmentStats.appointmentsByMonth.map(data => data.count) || [10, 15, 20, 25, 30, 35],
        backgroundColor: 'rgba(75, 192, 192, 0.5)',
        borderColor: 'rgba(75, 192, 192, 1)',
        borderWidth: 1,
        tension: 0.4
      },
    ],
  };

  // University distribution data
  const universityData = {
    labels: analytics.universityDistribution.map(uni => uni.universityName) || ['University A', 'University B', 'University C'],
    datasets: [
      {
        label: 'Students per University',
        data: analytics.universityDistribution.map(uni => uni.studentCount) || [50, 30, 20],
        backgroundColor: [
          'rgba(255, 99, 132, 0.7)',
          'rgba(54, 162, 235, 0.7)',
          'rgba(255, 206, 86, 0.7)',
          'rgba(75, 192, 192, 0.7)',
          'rgba(153, 102, 255, 0.7)',
          'rgba(255, 159, 64, 0.7)',
        ],
        borderColor: [
          'rgba(255, 99, 132, 1)',
          'rgba(54, 162, 235, 1)',
          'rgba(255, 206, 86, 1)',
          'rgba(75, 192, 192, 1)',
          'rgba(153, 102, 255, 1)',
          'rgba(255, 159, 64, 1)',
        ],
        borderWidth: 1,
      },
    ],
  };

  // Gender distribution data
  const genderData = {
    labels: analytics.patientDemographics.genderDistribution.map(item => item.gender) || ['Male', 'Female', 'Other'],
    datasets: [
      {
        label: 'Gender Distribution',
        data: analytics.patientDemographics.genderDistribution.map(item => item.count) || [45, 55, 5],
        backgroundColor: [
          'rgba(54, 162, 235, 0.7)',
          'rgba(255, 99, 132, 0.7)',
          'rgba(255, 206, 86, 0.7)',
        ],
        borderColor: [
          'rgba(54, 162, 235, 1)',
          'rgba(255, 99, 132, 1)',
          'rgba(255, 206, 86, 1)',
        ],
        borderWidth: 1,
      },
    ],
  };

  // Age distribution data
  const ageData = {
    labels: analytics.patientDemographics.ageDistribution.map(item => item.ageGroup) || ['0-18', '19-30', '31-45', '46-60', '60+'],
    datasets: [
      {
        label: 'Age Distribution',
        data: analytics.patientDemographics.ageDistribution.map(item => item.count) || [15, 25, 30, 20, 10],
        backgroundColor: 'rgba(153, 102, 255, 0.7)',
        borderColor: 'rgba(153, 102, 255, 1)',
        borderWidth: 1,
      },
    ],
  };

  // Procedure types data
  const procedureData = {
    labels: analytics.procedureTypes.map(item => item.procedure) || ['Filling', 'Extraction', 'Root Canal', 'Cleaning', 'Crown'],
    datasets: [
      {
        label: 'Procedure Types',
        data: analytics.procedureTypes.map(item => item.count) || [40, 25, 15, 35, 20],
        backgroundColor: [
          'rgba(255, 99, 132, 0.7)',
          'rgba(54, 162, 235, 0.7)',
          'rgba(255, 206, 86, 0.7)',
          'rgba(75, 192, 192, 0.7)',
          'rgba(153, 102, 255, 0.7)',
        ],
        borderColor: [
          'rgba(255, 99, 132, 1)',
          'rgba(54, 162, 235, 1)',
          'rgba(255, 206, 86, 1)',
          'rgba(75, 192, 192, 1)',
          'rgba(153, 102, 255, 1)',
        ],
        borderWidth: 1,
      },
    ],
  };

  // Render the appropriate chart based on type
  const renderChart = (data, type, height = '300px') => {
    const chartProps = {
      data,
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'top',
          },
        },
      },
      height
    };

    switch (type) {
      case 'bar':
        return <Bar {...chartProps} />;
      case 'line':
        return <Line {...chartProps} />;
      case 'pie':
        return <Pie {...chartProps} />;
      case 'doughnut':
        return <Doughnut {...chartProps} />;
      default:
        return <Bar {...chartProps} />;
    }
  };

  if (loading) return <Loader />;

  if (error) {
    return (
      <div className="flex h-screen bg-gray-50">
        <SuperAdminSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
        <div className="flex-1 flex flex-col overflow-hidden">
          <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
          <div className="flex-1 overflow-y-auto p-6 bg-gradient-to-br from-blue-50 to-white">
            <div className="max-w-7xl mx-auto">
              <div className="bg-white rounded-lg shadow-md p-6 text-center">
                <h2 className="text-2xl font-bold text-red-600 mb-4">Error</h2>
                <p className="text-gray-700">{error}</p>
                <button
                  onClick={() => navigate('/login')}
                  className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                  Go to Login
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gray-50">
      <SuperAdminSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />

      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />

        <div className="flex-1 overflow-y-auto p-4 md:p-6 bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 gap-6">
            {/* Header */}
            <motion.div variants={item} className="bg-gradient-to-r from-[#0077B6] to-[#20B2AA] rounded-xl shadow-lg p-6 text-white">
              <div className="flex flex-col md:flex-row md:justify-between md:items-center">
                <div>
                  <h1 className="text-3xl font-bold mb-2">Analytics Dashboard</h1>
                  <p className="opacity-80">Comprehensive insights into your dental management system</p>
                </div>
                <div className="mt-4 md:mt-0">
                  <label htmlFor="timeRange" className="block text-sm font-medium text-white mb-2">Time Range</label>
                  <select
                    id="timeRange"
                    value={timeRange}
                    onChange={(e) => setTimeRange(e.target.value)}
                    className="bg-white bg-opacity-20 text-white border border-white border-opacity-30 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50"
                  >
                    <option value="1month">Last Month</option>
                    <option value="3months">Last 3 Months</option>
                    <option value="6months">Last 6 Months</option>
                    <option value="1year">Last Year</option>
                    <option value="all">All Time</option>
                  </select>
                </div>
              </div>
            </motion.div>

            {/* Tab Navigation */}
            <motion.div variants={item} className="bg-white rounded-xl shadow-md overflow-hidden">
              <div className="flex overflow-x-auto">
                <button
                  onClick={() => setActiveTab('overview')}
                  className={`flex items-center px-6 py-4 text-sm font-medium border-b-2 whitespace-nowrap ${
                    activeTab === 'overview'
                      ? 'border-[#0077B6] text-[#0077B6]'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <FaChartBar className="mr-2" />
                  Overview
                </button>
                <button
                  onClick={() => setActiveTab('business')}
                  className={`flex items-center px-6 py-4 text-sm font-medium border-b-2 whitespace-nowrap ${
                    activeTab === 'business'
                      ? 'border-[#0077B6] text-[#0077B6]'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <FaChartBar className="mr-2" />
                  Business
                </button>
                <button
                  onClick={() => setActiveTab('accounts')}
                  className={`flex items-center px-6 py-4 text-sm font-medium border-b-2 whitespace-nowrap ${
                    activeTab === 'accounts'
                      ? 'border-[#0077B6] text-[#0077B6]'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <FaUserFriends className="mr-2" />
                  Accounts
                </button>
                <button
                  onClick={() => setActiveTab('universities')}
                  className={`flex items-center px-6 py-4 text-sm font-medium border-b-2 whitespace-nowrap ${
                    activeTab === 'universities'
                      ? 'border-[#0077B6] text-[#0077B6]'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <FaHospital className="mr-2" />
                  Universities
                </button>
                <button
                  onClick={() => setActiveTab('patients')}
                  className={`flex items-center px-6 py-4 text-sm font-medium border-b-2 whitespace-nowrap ${
                    activeTab === 'patients'
                      ? 'border-[#0077B6] text-[#0077B6]'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <FaUserInjured className="mr-2" />
                  Patients
                </button>
                <button
                  onClick={() => setActiveTab('appointments')}
                  className={`flex items-center px-6 py-4 text-sm font-medium border-b-2 whitespace-nowrap ${
                    activeTab === 'appointments'
                      ? 'border-[#0077B6] text-[#0077B6]'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <FaCalendarAlt className="mr-2" />
                  Appointments
                </button>
                <button
                  onClick={() => setActiveTab('performance')}
                  className={`flex items-center px-6 py-4 text-sm font-medium border-b-2 whitespace-nowrap ${
                    activeTab === 'performance'
                      ? 'border-[#0077B6] text-[#0077B6]'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <FaShieldAlt className="mr-2" />
                  Performance
                </button>
                <button
                  onClick={() => setActiveTab('activity')}
                  className={`flex items-center px-6 py-4 text-sm font-medium border-b-2 whitespace-nowrap ${
                    activeTab === 'activity'
                      ? 'border-[#0077B6] text-[#0077B6]'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <FaClipboardList className="mr-2" />
                  Activity
                </button>
              </div>
            </motion.div>

            {/* Tab Content */}
            <div className="mt-6">
              {/* Overview Tab */}
              {activeTab === 'overview' && (
                <div>
                  {/* Summary Cards */}
                  <motion.div variants={item} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                    <div className="bg-white rounded-xl shadow-md p-5 border-l-4 border-[#0077B6]">
                      <h3 className="text-gray-500 text-sm font-medium">Total Accounts</h3>
                      <p className="text-3xl font-bold text-[#0077B6]">{analytics.counts.totalAccounts}</p>
                    </div>
                    <div className="bg-white rounded-xl shadow-md p-5 border-l-4 border-[#20B2AA]">
                      <h3 className="text-gray-500 text-sm font-medium">Total Universities</h3>
                      <p className="text-3xl font-bold text-[#20B2AA]">{analytics.counts.totalUniversities}</p>
                    </div>
                    <div className="bg-white rounded-xl shadow-md p-5 border-l-4 border-[#0077B6]">
                      <h3 className="text-gray-500 text-sm font-medium">Total Patients</h3>
                      <p className="text-3xl font-bold text-[#0077B6]">{analytics.counts.totalPatients}</p>
                    </div>
                    <div className="bg-white rounded-xl shadow-md p-5 border-l-4 border-[#20B2AA]">
                      <h3 className="text-gray-500 text-sm font-medium">Total Appointments</h3>
                      <p className="text-3xl font-bold text-[#20B2AA]">{analytics.counts.totalAppointments}</p>
                    </div>
                  </motion.div>

                  {/* Main Charts - Two Side by Side */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    {/* Account Distribution */}
                    <motion.div variants={item} className="bg-white rounded-xl shadow-md p-6">
                      <div className="flex justify-between items-center mb-4">
                        <h2 className="text-xl font-bold text-[#0077B6]">Account Distribution</h2>
                        <button
                          onClick={() => toggleChartType('accounts')}
                          className="px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-md hover:bg-[rgba(0,119,182,0.2)] transition-colors text-sm"
                        >
                          Change Chart Type
                        </button>
                      </div>
                      <div style={{ height: '300px' }}>
                        {renderChart(accountsData, chartType.accounts)}
                      </div>
                    </motion.div>

                    {/* Appointments Over Time */}
                    <motion.div variants={item} className="bg-white rounded-xl shadow-md p-6">
                      <div className="flex justify-between items-center mb-4">
                        <h2 className="text-xl font-bold text-[#0077B6]">Appointments Over Time</h2>
                        <button
                          onClick={() => toggleChartType('appointments')}
                          className="px-3 py-1 bg-[rgba(0,119,182,0.1)] text-[#0077B6] rounded-md hover:bg-[rgba(0,119,182,0.2)] transition-colors text-sm"
                        >
                          Change Chart Type
                        </button>
                      </div>
                      <div style={{ height: '300px' }}>
                        {renderChart(appointmentsData, chartType.appointments)}
                      </div>
                    </motion.div>
                  </div>

                  {/* Recent Activity */}
                  <motion.div variants={item} className="bg-white rounded-xl shadow-md p-6">
                    <h2 className="text-xl font-bold text-[#0077B6] mb-4">Recent Activity</h2>
                    <div className="overflow-hidden">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {analytics.recentActivity.slice(0, 5).map((activity, index) => (
                            <tr key={index} className="hover:bg-gray-50">
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{activity.action}</td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{activity.user}</td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {new Date(activity.date).toLocaleString()}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </motion.div>
                </div>
              )}

              {/* Business Tab */}
              {activeTab === 'business' && (
                <div>
                  {/* Key Business Metrics */}
                  <motion.div variants={item} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                    <div className="bg-white rounded-xl shadow-md p-5 border-l-4 border-green-500">
                      <h3 className="text-gray-500 text-sm font-medium">Active Users</h3>
                      <p className="text-3xl font-bold text-green-600">{analytics.businessMetrics.activeUsers.total}</p>
                      <p className="text-xs text-gray-400 mt-1">In selected period</p>
                    </div>
                    <div className="bg-white rounded-xl shadow-md p-5 border-l-4 border-blue-500">
                      <h3 className="text-gray-500 text-sm font-medium">University Adoption</h3>
                      <p className="text-3xl font-bold text-blue-600">{Math.round(analytics.businessMetrics.universityAdoption.adoptionRate)}%</p>
                      <p className="text-xs text-gray-400 mt-1">{analytics.businessMetrics.universityAdoption.active} of {analytics.businessMetrics.universityAdoption.total} universities</p>
                    </div>
                    <div className="bg-white rounded-xl shadow-md p-5 border-l-4 border-purple-500">
                      <h3 className="text-gray-500 text-sm font-medium">User Retention</h3>
                      <p className="text-3xl font-bold text-purple-600">{analytics.businessMetrics.retentionMetrics.userRetention}%</p>
                      <p className="text-xs text-gray-400 mt-1">Active users</p>
                    </div>
                    <div className="bg-white rounded-xl shadow-md p-5 border-l-4 border-orange-500">
                      <h3 className="text-gray-500 text-sm font-medium">Monthly Growth</h3>
                      <p className="text-3xl font-bold text-orange-600">{analytics.businessMetrics.growthRate.monthly}</p>
                      <p className="text-xs text-gray-400 mt-1">New users this month</p>
                    </div>
                  </motion.div>

                  {/* Active Users by Role */}
                  <motion.div variants={item} className="bg-white rounded-xl shadow-md p-6 mb-6">
                    <h2 className="text-xl font-bold text-[#0077B6] mb-4">Active Users by Role</h2>
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                      {Object.entries(analytics.businessMetrics.activeUsers.byRole).map(([role, count]) => (
                        <div key={role} className="bg-gray-50 rounded-lg p-4 text-center">
                          <div className="text-2xl font-bold text-[#0077B6]">{count}</div>
                          <div className="text-sm text-gray-600 capitalize">{role}s</div>
                        </div>
                      ))}
                    </div>
                  </motion.div>

                  {/* System Usage Metrics */}
                  <motion.div variants={item} className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div className="bg-white rounded-xl shadow-md p-6">
                      <h3 className="text-lg font-bold text-[#0077B6] mb-4">Appointment Efficiency</h3>
                      <div className="space-y-4">
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">Completion Rate</span>
                          <span className="font-bold text-green-600">{Math.round(analytics.systemUsage.appointmentMetrics.completionRate)}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-green-500 h-2 rounded-full"
                            style={{ width: `${analytics.systemUsage.appointmentMetrics.completionRate}%` }}
                          ></div>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">Cancellation Rate</span>
                          <span className="font-bold text-red-600">{Math.round(analytics.systemUsage.appointmentMetrics.cancellationRate)}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-red-500 h-2 rounded-full"
                            style={{ width: `${analytics.systemUsage.appointmentMetrics.cancellationRate}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>

                    <div className="bg-white rounded-xl shadow-md p-6">
                      <h3 className="text-lg font-bold text-[#0077B6] mb-4">Patient Management</h3>
                      <div className="space-y-4">
                        <div className="text-center">
                          <div className="text-3xl font-bold text-[#20B2AA]">{analytics.systemUsage.patientMetrics.averagePatientsPerStudent}</div>
                          <div className="text-sm text-gray-600">Avg Patients per Student</div>
                        </div>
                        <div className="text-center">
                          <div className="text-3xl font-bold text-[#0077B6]">{analytics.systemUsage.patientMetrics.treatmentCompletionRate}%</div>
                          <div className="text-sm text-gray-600">Treatment Completion Rate</div>
                        </div>
                      </div>
                    </div>
                  </motion.div>

                  {/* Feature Usage */}
                  <motion.div variants={item} className="bg-white rounded-xl shadow-md p-6">
                    <h3 className="text-lg font-bold text-[#0077B6] mb-4">Feature Usage Statistics</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h4 className="font-semibold text-gray-700 mb-3">Most Used Features</h4>
                        <div className="space-y-2">
                          {analytics.systemUsage.featureUsage.mostUsedFeatures.slice(0, 5).map((feature, index) => (
                            <div key={index} className="flex justify-between items-center p-2 bg-green-50 rounded">
                              <span className="text-sm text-gray-700">{feature.feature}</span>
                              <span className="font-bold text-green-600">{feature.usage}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-700 mb-3">Least Used Features</h4>
                        <div className="space-y-2">
                          {analytics.systemUsage.featureUsage.leastUsedFeatures.slice(0, 5).map((feature, index) => (
                            <div key={index} className="flex justify-between items-center p-2 bg-red-50 rounded">
                              <span className="text-sm text-gray-700">{feature.feature}</span>
                              <span className="font-bold text-red-600">{feature.usage}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </motion.div>
                </div>
              )}

              {/* Accounts Tab */}
              {activeTab === 'accounts' && (
                <div>
                  {/* Account Distribution Chart */}
                  <motion.div variants={item} className="bg-white rounded-xl shadow-md p-6 mb-6">
                    <div className="flex justify-between items-center mb-4">
                      <h2 className="text-xl font-bold text-gray-800">Account Distribution</h2>
                      <button
                        onClick={() => toggleChartType('accounts')}
                        className="px-3 py-1 bg-blue-100 text-blue-600 rounded-md hover:bg-blue-200 transition-colors text-sm"
                      >
                        Change Chart Type
                      </button>
                    </div>
                    <div style={{ height: '400px' }}>
                      {renderChart(accountsData, chartType.accounts)}
                    </div>
                  </motion.div>

                  {/* Account Growth Over Time */}
                  <motion.div variants={item} className="bg-white rounded-xl shadow-md p-6">
                    <h2 className="text-xl font-bold text-gray-800 mb-4">Account Growth Over Time</h2>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                        <h3 className="text-sm font-medium text-blue-800">Students</h3>
                        <p className="text-2xl font-bold text-blue-600">{analytics.counts.totalStudents}</p>
                      </div>
                      <div className="bg-green-50 rounded-lg p-4 border border-green-200">
                        <h3 className="text-sm font-medium text-green-800">Supervisors</h3>
                        <p className="text-2xl font-bold text-green-600">{analytics.counts.totalSupervisors}</p>
                      </div>
                      <div className="bg-purple-50 rounded-lg p-4 border border-purple-200">
                        <h3 className="text-sm font-medium text-purple-800">Admins</h3>
                        <p className="text-2xl font-bold text-purple-600">{analytics.counts.totalAdmins}</p>
                      </div>
                    </div>
                  </motion.div>
                </div>
              )}

              {/* Universities Tab */}
              {activeTab === 'universities' && (
                <div>
                  {/* University Distribution */}
                  <motion.div variants={item} className="bg-white rounded-xl shadow-md p-6 mb-6">
                    <div className="flex justify-between items-center mb-4">
                      <h2 className="text-xl font-bold text-gray-800">University Distribution</h2>
                      <button
                        onClick={() => toggleChartType('universities')}
                        className="px-3 py-1 bg-blue-100 text-blue-600 rounded-md hover:bg-blue-200 transition-colors text-sm"
                      >
                        Change Chart Type
                      </button>
                    </div>
                    <div style={{ height: '400px' }}>
                      {renderChart(universityData, chartType.universities)}
                    </div>
                  </motion.div>

                  {/* University Stats */}
                  <motion.div variants={item} className="bg-white rounded-xl shadow-md p-6">
                    <h2 className="text-xl font-bold text-gray-800 mb-4">University Statistics</h2>
                    <div className="overflow-hidden">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">University</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Students</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {analytics.universityDistribution.map((uni, index) => (
                            <tr key={index} className="hover:bg-gray-50">
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{uni.universityName}</td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{uni.studentCount}</td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{uni.universityId}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </motion.div>
                </div>
              )}

              {/* Patients Tab */}
              {activeTab === 'patients' && (
                <div>
                  {/* Patient Demographics - Two Charts Side by Side */}
                  <motion.div variants={item} className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    {/* Gender Distribution */}
                    <div className="bg-white rounded-xl shadow-md p-6">
                      <div className="flex justify-between items-center mb-4">
                        <h2 className="text-xl font-bold text-gray-800">Gender Distribution</h2>
                        <button
                          onClick={() => toggleChartType('gender')}
                          className="px-3 py-1 bg-blue-100 text-blue-600 rounded-md hover:bg-blue-200 transition-colors text-sm"
                        >
                          Change Chart Type
                        </button>
                      </div>
                      <div style={{ height: '300px' }}>
                        {renderChart(genderData, chartType.gender)}
                      </div>
                    </div>

                    {/* Age Distribution */}
                    <div className="bg-white rounded-xl shadow-md p-6">
                      <div className="flex justify-between items-center mb-4">
                        <h2 className="text-xl font-bold text-gray-800">Age Distribution</h2>
                        <button
                          onClick={() => toggleChartType('age')}
                          className="px-3 py-1 bg-blue-100 text-blue-600 rounded-md hover:bg-blue-200 transition-colors text-sm"
                        >
                          Change Chart Type
                        </button>
                      </div>
                      <div style={{ height: '300px' }}>
                        {renderChart(ageData, chartType.age)}
                      </div>
                    </div>
                  </motion.div>

                  {/* Procedure Types */}
                  <motion.div variants={item} className="bg-white rounded-xl shadow-md p-6">
                    <div className="flex justify-between items-center mb-4">
                      <h2 className="text-xl font-bold text-gray-800">Procedure Types</h2>
                      <button
                        onClick={() => toggleChartType('procedures')}
                        className="px-3 py-1 bg-blue-100 text-blue-600 rounded-md hover:bg-blue-200 transition-colors text-sm"
                      >
                        Change Chart Type
                      </button>
                    </div>
                    <div style={{ height: '300px' }}>
                      {renderChart(procedureData, chartType.procedures)}
                    </div>
                  </motion.div>
                </div>
              )}

              {/* Appointments Tab */}
              {activeTab === 'appointments' && (
                <div>
                  {/* Appointments Over Time */}
                  <motion.div variants={item} className="bg-white rounded-xl shadow-md p-6 mb-6">
                    <div className="flex justify-between items-center mb-4">
                      <h2 className="text-xl font-bold text-gray-800">Appointments Over Time</h2>
                      <button
                        onClick={() => toggleChartType('appointments')}
                        className="px-3 py-1 bg-blue-100 text-blue-600 rounded-md hover:bg-blue-200 transition-colors text-sm"
                      >
                        Change Chart Type
                      </button>
                    </div>
                    <div style={{ height: '400px' }}>
                      {renderChart(appointmentsData, chartType.appointments)}
                    </div>
                  </motion.div>

                  {/* Appointment Status */}
                  <motion.div variants={item} className="bg-white rounded-xl shadow-md p-6">
                    <h2 className="text-xl font-bold text-gray-800 mb-4">Appointment Status</h2>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="bg-green-50 rounded-lg p-4 border border-green-200">
                        <h3 className="text-sm font-medium text-green-800">Completed</h3>
                        <p className="text-2xl font-bold text-green-600">{analytics.appointmentStats.statusCounts?.completed || 0}</p>
                      </div>
                      <div className="bg-yellow-50 rounded-lg p-4 border border-yellow-200">
                        <h3 className="text-sm font-medium text-yellow-800">Pending</h3>
                        <p className="text-2xl font-bold text-yellow-600">{analytics.appointmentStats.statusCounts?.pending || 0}</p>
                      </div>
                      <div className="bg-red-50 rounded-lg p-4 border border-red-200">
                        <h3 className="text-sm font-medium text-red-800">Cancelled</h3>
                        <p className="text-2xl font-bold text-red-600">{analytics.appointmentStats.statusCounts?.cancelled || 0}</p>
                      </div>
                    </div>
                  </motion.div>
                </div>
              )}

              {/* Performance Tab */}
              {activeTab === 'performance' && (
                <div>
                  {/* Performance Metrics */}
                  <motion.div variants={item} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                    <div className="bg-white rounded-xl shadow-md p-5 border-l-4 border-green-500">
                      <h3 className="text-gray-500 text-sm font-medium">Response Time</h3>
                      <p className="text-3xl font-bold text-green-600">{analytics.performanceMetrics.responseTime}ms</p>
                      <p className="text-xs text-gray-400 mt-1">Average response time</p>
                    </div>
                    <div className="bg-white rounded-xl shadow-md p-5 border-l-4 border-blue-500">
                      <h3 className="text-gray-500 text-sm font-medium">System Uptime</h3>
                      <p className="text-3xl font-bold text-blue-600">{analytics.performanceMetrics.uptime}%</p>
                      <p className="text-xs text-gray-400 mt-1">System availability</p>
                    </div>
                    <div className="bg-white rounded-xl shadow-md p-5 border-l-4 border-yellow-500">
                      <h3 className="text-gray-500 text-sm font-medium">Error Rate</h3>
                      <p className="text-3xl font-bold text-yellow-600">{analytics.performanceMetrics.errorRate}%</p>
                      <p className="text-xs text-gray-400 mt-1">System error rate</p>
                    </div>
                    <div className="bg-white rounded-xl shadow-md p-5 border-l-4 border-purple-500">
                      <h3 className="text-gray-500 text-sm font-medium">Data Quality</h3>
                      <p className="text-3xl font-bold text-purple-600">{analytics.performanceMetrics.dataQuality.completeness}%</p>
                      <p className="text-xs text-gray-400 mt-1">Data completeness</p>
                    </div>
                  </motion.div>

                  {/* System Health */}
                  <motion.div variants={item} className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div className="bg-white rounded-xl shadow-md p-6">
                      <h3 className="text-lg font-bold text-[#0077B6] mb-4">System Performance</h3>
                      <div className="space-y-4">
                        <div>
                          <div className="flex justify-between items-center mb-2">
                            <span className="text-gray-600">Response Time</span>
                            <span className="font-bold text-green-600">{analytics.performanceMetrics.responseTime}ms</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-green-500 h-2 rounded-full"
                              style={{ width: `${Math.min((300 - analytics.performanceMetrics.responseTime) / 300 * 100, 100)}%` }}
                            ></div>
                          </div>
                        </div>
                        <div>
                          <div className="flex justify-between items-center mb-2">
                            <span className="text-gray-600">System Uptime</span>
                            <span className="font-bold text-blue-600">{analytics.performanceMetrics.uptime}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-blue-500 h-2 rounded-full"
                              style={{ width: `${analytics.performanceMetrics.uptime}%` }}
                            ></div>
                          </div>
                        </div>
                        <div>
                          <div className="flex justify-between items-center mb-2">
                            <span className="text-gray-600">Error Rate</span>
                            <span className="font-bold text-red-600">{analytics.performanceMetrics.errorRate}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-red-500 h-2 rounded-full"
                              style={{ width: `${analytics.performanceMetrics.errorRate * 20}%` }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="bg-white rounded-xl shadow-md p-6">
                      <h3 className="text-lg font-bold text-[#0077B6] mb-4">Data Quality Metrics</h3>
                      <div className="space-y-6">
                        <div className="text-center">
                          <div className="relative inline-flex items-center justify-center w-24 h-24">
                            <svg className="w-24 h-24 transform -rotate-90" viewBox="0 0 36 36">
                              <path
                                className="text-gray-300"
                                stroke="currentColor"
                                strokeWidth="3"
                                fill="none"
                                d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                              />
                              <path
                                className="text-[#20B2AA]"
                                stroke="currentColor"
                                strokeWidth="3"
                                strokeDasharray={`${analytics.performanceMetrics.dataQuality.completeness}, 100`}
                                strokeLinecap="round"
                                fill="none"
                                d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                              />
                            </svg>
                            <div className="absolute inset-0 flex items-center justify-center">
                              <span className="text-xl font-bold text-[#0077B6]">{analytics.performanceMetrics.dataQuality.completeness}%</span>
                            </div>
                          </div>
                          <div className="text-sm text-gray-600 mt-2">Data Completeness</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-[#0077B6]">{analytics.performanceMetrics.dataQuality.accuracy}%</div>
                          <div className="text-sm text-gray-600">Data Accuracy</div>
                        </div>
                      </div>
                    </div>
                  </motion.div>

                  {/* Performance Recommendations */}
                  <motion.div variants={item} className="bg-white rounded-xl shadow-md p-6">
                    <h3 className="text-lg font-bold text-[#0077B6] mb-4">Performance Recommendations</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                        <h4 className="font-semibold text-green-800 mb-2">✅ Good Performance</h4>
                        <ul className="text-sm text-green-700 space-y-1">
                          <li>• System uptime is excellent ({analytics.performanceMetrics.uptime}%)</li>
                          <li>• Response times are within acceptable range</li>
                          <li>• Data quality metrics are strong</li>
                        </ul>
                      </div>
                      <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                        <h4 className="font-semibold text-yellow-800 mb-2">⚠️ Areas for Improvement</h4>
                        <ul className="text-sm text-yellow-700 space-y-1">
                          <li>• Monitor error rates closely</li>
                          <li>• Consider optimizing database queries</li>
                          <li>• Implement data validation improvements</li>
                        </ul>
                      </div>
                    </div>
                  </motion.div>
                </div>
              )}

              {/* Activity Tab */}
              {activeTab === 'activity' && (
                <div>
                  {/* Recent Activity */}
                  <motion.div variants={item} className="bg-white rounded-xl shadow-md p-6">
                    <h2 className="text-xl font-bold text-gray-800 mb-4">Recent Activity</h2>
                    <div className="overflow-hidden">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {analytics.recentActivity.map((activity, index) => (
                            <tr key={index} className="hover:bg-gray-50">
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{activity.action}</td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{activity.user}</td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {new Date(activity.date).toLocaleString()}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </motion.div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
    </div>
  );
};

export default Analytics;