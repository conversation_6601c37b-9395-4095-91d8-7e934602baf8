# Google OAuth Fix for Railway Deployment

## 🚨 Error: "The given origin is not allowed for the given client ID"

This error occurs because your Google OAuth client ID is not configured to allow requests from your Railway domain.

## ✅ Solution Steps

### Step 1: Update Google Cloud Console OAuth Configuration

1. **Go to Google Cloud Console**:
   - Visit: https://console.cloud.google.com/apis/credentials?project=odenta-82359

2. **Find Your OAuth 2.0 Client ID**:
   - Look for the client ID: `632651401940-dnsobsvoiqdqe9l6o1g5qk2ebkkkehe2.apps.googleusercontent.com`
   - Click on it to edit

3. **Add Authorized Origins**:
   Add these to the "Authorized JavaScript origins" section:
   ```
   http://localhost:3000
   https://odenta.vercel.app
   https://odenta-backend-production-1b94.up.railway.app
   https://your-frontend-domain.vercel.app
   ```

4. **Add Authorized Redirect URIs**:
   Add these to the "Authorized redirect URIs" section:
   ```
   http://localhost:3000/auth/callback
   https://odenta.vercel.app/auth/callback
   https://your-frontend-domain.vercel.app/auth/callback
   ```

5. **Save Changes**

### Step 2: Update Frontend Environment Variables

In your frontend project, update the environment variables:

**For Vercel deployment:**
```env
REACT_APP_GOOGLE_CLIENT_ID=632651401940-dnsobsvoiqdqe9l6o1g5qk2ebkkkehe2.apps.googleusercontent.com
REACT_APP_API_URL=https://odenta-backend-production-1b94.up.railway.app
```

**For local development:**
```env
REACT_APP_GOOGLE_CLIENT_ID=632651401940-dnsobsvoiqdqe9l6o1g5qk2ebkkkehe2.apps.googleusercontent.com
REACT_APP_API_URL=http://localhost:5000
```

### Step 3: Update Backend Environment Variables

In Railway dashboard, add these environment variables:

```env
# Google OAuth Configuration
GOOGLE_CLIENT_ID=632651401940-dnsobsvoiqdqe9l6o1g5qk2ebkkkehe2.apps.googleusercontent.com

# Other existing variables...
PORT=8080
NODE_ENV=production
MONGO_URI=your_mongodb_uri
JWT_SECRET=your_jwt_secret
JWT_REFRESH_SECRET=your_refresh_secret
FRONTEND_URL=https://odenta.vercel.app
```

### Step 4: Update CORS Configuration

The CORS configuration in your backend should already include your Railway domain, but verify it's correct:

```javascript
const allowedOrigins = [
  'http://localhost:3000',
  'https://odenta.vercel.app',
  'https://Odenta.vercel.app',
  'https://odenta.vercel.app/',
  'https://Odenta.vercel.app/',
  'https://odenta-backend-production-1b94.up.railway.app'
];
```

## 🔧 Alternative Solutions

### Option 1: Create New OAuth Client ID (Recommended)

If the above doesn't work, create a new OAuth client ID:

1. **Go to Google Cloud Console**:
   - Visit: https://console.cloud.google.com/apis/credentials?project=odenta-82359

2. **Create New OAuth 2.0 Client ID**:
   - Click "Create Credentials" → "OAuth 2.0 Client ID"
   - Application type: "Web application"
   - Name: "ODenta Production"
   - Authorized origins: 
     - `http://localhost:3000`
     - `https://odenta.vercel.app`
     - `https://odenta-backend-production-1b94.up.railway.app`
   - Authorized redirect URIs:
     - `http://localhost:3000/auth/callback`
     - `https://odenta.vercel.app/auth/callback`

3. **Use the new Client ID** in your environment variables

### Option 2: Firebase Authentication (Alternative)

Instead of Google OAuth, you can use Firebase Authentication:

1. **Enable Firebase Authentication**:
   - Go to Firebase Console → Authentication
   - Enable Google provider
   - Add your domains to authorized domains

2. **Update Frontend**:
   - Use Firebase Auth instead of Google Identity Services
   - This bypasses the OAuth client ID issue

## 🎯 Verification Steps

### Step 1: Test Google Sign-In
1. Go to your frontend application
2. Try to sign in with Google
3. Should work without the 403 error

### Step 2: Check Browser Console
1. Open browser developer tools
2. Go to Console tab
3. Should not see any Google OAuth errors

### Step 3: Test Backend Integration
1. Sign in with Google
2. Check if the backend receives the token
3. Verify user creation/login works

## 📋 Common Issues and Solutions

### Issue 1: Still getting 403 error
**Solution**: 
- Double-check the authorized origins in Google Cloud Console
- Make sure the domain exactly matches (including https://)
- Wait a few minutes for changes to propagate

### Issue 2: Google Sign-In button not appearing
**Solution**:
- Check if `REACT_APP_GOOGLE_CLIENT_ID` is set correctly
- Verify the Google Identity Services script is loading
- Check browser console for JavaScript errors

### Issue 3: Backend not receiving Google tokens
**Solution**:
- Verify `GOOGLE_CLIENT_ID` is set in Railway environment variables
- Check backend logs for Google OAuth errors
- Ensure the client ID matches between frontend and backend

## 🚀 Quick Fix Summary

1. **Update Google Cloud Console** - Add your Railway domain to authorized origins
2. **Update environment variables** - Set the correct client ID in both frontend and backend
3. **Redeploy** - Push changes to trigger new deployments
4. **Test** - Verify Google Sign-In works

The key issue is that Google OAuth requires explicit permission for each domain that will use the client ID. Adding your Railway domain to the authorized origins should resolve the 403 error. 