import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { motion } from 'framer-motion';
import { FaFlask, FaUniversity, FaBuilding, FaCheck, FaTimes, FaClock, FaUser, FaCalendarAlt, FaEye } from 'react-icons/fa';
import { formatDate } from '../utils/dateUtils';
import AssistantSidebar from './AssistantSidebar';
import Navbar from '../student/Navbar';

const container = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const item = {
  hidden: { y: 20, opacity: 0 },
  show: {
    y: 0,
    opacity: 1,
  },
};

const LabRequests = () => {
  const [labRequests, setLabRequests] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [filter, setFilter] = useState('all');
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [selectedRequest, setSelectedRequest] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [actionType, setActionType] = useState('');
  const [responseNotes, setResponseNotes] = useState('');
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    fetchLabRequests();
  }, []);

  const fetchLabRequests = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/lab-requests`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      setLabRequests(response.data);
    } catch (error) {
      console.error('Error fetching lab requests:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAction = (request, action) => {
    setSelectedRequest(request);
    setActionType(action);
    setResponseNotes('');
    setShowModal(true);
  };

  const submitAction = async () => {
    if (!selectedRequest || !actionType) return;

    setSubmitting(true);
    try {
      const token = localStorage.getItem('token');
      await axios.put(`${process.env.REACT_APP_API_URL}/api/lab-requests/${selectedRequest.id || selectedRequest._id}`, {
        status: actionType,
        responseNotes
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });

      // Refresh the list
      fetchLabRequests();
      setShowModal(false);
      setSelectedRequest(null);
      setActionType('');
      setResponseNotes('');
    } catch (error) {
      console.error('Error updating lab request:', error);
      console.error('Error details:', error.response?.data);
      console.error('Selected request:', selectedRequest);
      alert(`Error updating lab request: ${error.response?.data?.message || error.message}`);
    } finally {
      setSubmitting(false);
    }
  };

  const getStatusColor = (status) => {
    if (!status) return 'text-gray-600 bg-gray-100 border-gray-200';
    switch (status) {
      case 'pending': return 'text-orange-600 bg-orange-100 border-orange-200';
      case 'approved': return 'text-green-600 bg-green-100 border-green-200';
      case 'rejected': return 'text-red-600 bg-red-100 border-red-200';
      case 'completed': return 'text-[#0077B6] bg-[rgba(0,119,182,0.1)] border-[rgba(0,119,182,0.2)]';
      default: return 'text-gray-600 bg-gray-100 border-gray-200';
    }
  };

  const getStatusIcon = (status) => {
    if (!status) return <FaFlask className="h-4 w-4" />;
    switch (status) {
      case 'pending': return <FaClock className="h-4 w-4" />;
      case 'approved': return <FaCheck className="h-4 w-4" />;
      case 'rejected': return <FaTimes className="h-4 w-4" />;
      case 'completed': return <FaCheck className="h-4 w-4" />;
      default: return <FaFlask className="h-4 w-4" />;
    }
  };

  const getLabTypeIcon = (labType) => {
    return labType === 'university' ? 
      <FaUniversity className="h-5 w-5 text-blue-600" /> : 
      <FaBuilding className="h-5 w-5 text-green-600" />;
  };

  const filteredRequests = labRequests.filter(request => {
    if (filter === 'all') return true;
    return request.status === filter;
  });

  const getStatusCounts = () => {
    return {
      all: labRequests.length,
      pending: labRequests.filter(r => r.status === 'pending').length,
      approved: labRequests.filter(r => r.status === 'approved').length,
      rejected: labRequests.filter(r => r.status === 'rejected').length,
      completed: labRequests.filter(r => r.status === 'completed').length,
    };
  };

  const statusCounts = getStatusCounts();

  if (loading) {
    return (
      <div className="flex h-screen bg-gray-50">
        <AssistantSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
        <div className="flex-1 flex flex-col overflow-hidden">
          <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
          <main className="flex-1 overflow-y-auto p-6 bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white">
            <div className="max-w-7xl mx-auto">
              <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-8">
                <div className="animate-pulse">
                  <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
                  <div className="space-y-4">
                    {[1, 2, 3].map(i => (
                      <div key={i} className="h-24 bg-gray-200 rounded"></div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </main>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gray-50">
      <AssistantSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
        <main className="flex-1 overflow-y-auto p-4 sm:p-6 bg-gradient-to-br from-blue-50 to-white">
          <div className="max-w-7xl mx-auto">
            {error && (
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm"
              >
                <div className="flex items-center">
                  <svg className="w-5 h-5 text-red-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                  <p className="text-red-700 font-medium">{error}</p>
                </div>
              </motion.div>
            )}

            <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.5 }}>
              <div className="mb-6 sm:mb-8">
                <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold text-[#0077B6] mb-1">Lab Requests</h1>
                <p className="text-gray-600">Review and manage lab requests from students</p>
              </div>

              <motion.div
                variants={container}
                initial="hidden"
                whileInView="show"
                viewport={{ once: true }}
                className="space-y-6 sm:space-y-8"
              >
                <motion.div
                  variants={item}
                  className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 overflow-hidden"
                >
                  <div className="border-b border-gray-200">
                    <div className="py-3 sm:py-4 px-4 sm:px-6 text-center font-medium text-xs sm:text-sm text-[#0077B6] border-b-2 border-[#0077B6]"
                    >
                      Lab Request Management
                    </div>
                  </div>
                  <div className="p-4 sm:p-6">
                    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 sm:mb-6 gap-4">
                      <h2 className="text-lg sm:text-xl font-bold text-[#0077B6] flex items-center">
                        <FaFlask className="h-4 w-4 sm:h-5 sm:w-5 mr-2 text-[#0077B6]" />
                        Lab Requests
                      </h2>
                    </div>

                    {/* Filter Tabs */}
                    <div className="flex flex-wrap gap-2 mb-4 sm:mb-6">
                      {[
                        { key: 'all', label: 'All', count: statusCounts.all },
                        { key: 'pending', label: 'Pending', count: statusCounts.pending },
                        { key: 'approved', label: 'Approved', count: statusCounts.approved },
                        { key: 'completed', label: 'Completed', count: statusCounts.completed },
                        { key: 'rejected', label: 'Rejected', count: statusCounts.rejected },
                      ].map(({ key, label, count }) => (
                        <button
                          key={key}
                          onClick={() => setFilter(key)}
                          className={`px-3 sm:px-4 py-2 rounded-lg text-xs sm:text-sm font-medium transition-colors ${
                            filter === key
                              ? 'bg-[#0077B6] text-white'
                              : 'bg-gray-100 text-gray-600 hover:bg-[rgba(0,119,182,0.1)] hover:text-[#0077B6]'
                          }`}
                        >
                          {label} ({count})
                        </button>
                      ))}
                    </div>

                    {/* Lab Requests List */}
                    <div className="space-y-3 sm:space-y-4">
                      {filteredRequests.map((request) => (
                        <motion.div
                          key={request.id || request._id}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          className="bg-white border border-gray-200 rounded-lg p-4 sm:p-6 shadow-sm hover:shadow-md transition-shadow"
                        >
                          <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between mb-3 sm:mb-4">
                            <div className="flex items-center mb-3 sm:mb-0">
                              {getLabTypeIcon(request.labType)}
                              <div className="ml-3">
                                <h3 className="text-base sm:text-lg font-semibold text-[#333333]">
                                  {request.labType === 'university' ? 'University Lab' : 'Outside Lab'}
                                </h3>
                                <p className="text-xs sm:text-sm text-gray-600">Request ID: {(request.id || request._id) ? (request.id || request._id).slice(-8) : 'N/A'}</p>
                              </div>
                            </div>
                            <span className={`px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-medium border ${getStatusColor(request.status)}`}>
                              <div className="flex items-center">
                                {getStatusIcon(request.status)}
                                <span className="ml-1">{request.status ? request.status.charAt(0).toUpperCase() + request.status.slice(1) : 'Unknown'}</span>
                              </div>
                            </span>
                          </div>

                          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 mb-3 sm:mb-4">
                            <div className="flex items-center text-xs sm:text-sm text-gray-600">
                              <FaUser className="h-3 w-3 sm:h-4 sm:w-4 mr-2 text-[#0077B6]" />
                              <span className="font-medium">Student:</span>
                              <span className="ml-1">{request.studentName}</span>
                            </div>
                            <div className="flex items-center text-xs sm:text-sm text-gray-600">
                              <FaUser className="h-3 w-3 sm:h-4 sm:w-4 mr-2 text-[#0077B6]" />
                              <span className="font-medium">Patient:</span>
                              <span className="ml-1">{request.patientName}</span>
                            </div>
                            <div className="flex items-center text-xs sm:text-sm text-gray-600 sm:col-span-2 lg:col-span-1">
                              <FaCalendarAlt className="h-3 w-3 sm:h-4 sm:w-4 mr-2 text-[#0077B6]" />
                              <span className="font-medium">Submitted:</span>
                              <span className="ml-1">{formatDate(request.submitDate)}</span>
                            </div>
                          </div>

                          {request.notes && (
                            <div className="mb-3 sm:mb-4">
                              <p className="text-xs sm:text-sm text-gray-600">
                                <span className="font-medium">Notes:</span> {request.notes}
                              </p>
                            </div>
                          )}

                          {request.responseNotes && (
                            <div className="bg-[rgba(0,119,182,0.05)] p-3 rounded-lg border border-[rgba(0,119,182,0.1)] mb-3 sm:mb-4">
                              <p className="text-xs sm:text-sm text-gray-600">
                                <span className="font-medium">Response:</span> {request.responseNotes}
                              </p>
                              {request.responseDate && (
                                <p className="text-xs text-gray-500 mt-1">
                                  Responded on: {formatDate(request.responseDate)}
                                </p>
                              )}
                            </div>
                          )}

                          {/* Action Buttons */}
                          {request.status === 'pending' && (
                            <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
                              <button
                                onClick={() => handleAction(request, 'approved')}
                                className="w-full sm:w-auto px-3 sm:px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center text-xs sm:text-sm"
                              >
                                <FaCheck className="h-3 w-3 sm:h-4 sm:w-4 mr-2" />
                                Approve
                              </button>
                              <button
                                onClick={() => handleAction(request, 'rejected')}
                                className="w-full sm:w-auto px-3 sm:px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors flex items-center justify-center text-xs sm:text-sm"
                              >
                                <FaTimes className="h-3 w-3 sm:h-4 sm:w-4 mr-2" />
                                Reject
                              </button>
                            </div>
                          )}
                        </motion.div>
                      ))}
                    </div>
                  </div>
                </motion.div>
              </motion.div>
            </motion.div>
          </div>
        </main>
      </div>

      {/* Action Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            className="bg-white rounded-xl shadow-2xl p-6 max-w-md w-full mx-4"
          >
            <div className="text-center mb-6">
              <div className={`mx-auto flex items-center justify-center h-12 w-12 rounded-full mb-4 ${
                actionType === 'approved' ? 'bg-green-100' : 'bg-red-100'
              }`}>
                {actionType === 'approved' ? (
                  <FaCheck className="h-6 w-6 text-green-600" />
                ) : (
                  <FaTimes className="h-6 w-6 text-red-600" />
                )}
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {actionType === 'approved' ? 'Approve Request' : 'Reject Request'}
              </h3>
              <p className="text-sm text-gray-500">
                {actionType === 'approved' 
                  ? 'Are you sure you want to approve this lab request?' 
                  : 'Are you sure you want to reject this lab request?'
                }
              </p>
            </div>

            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Response Notes (Optional)
              </label>
              <textarea
                value={responseNotes}
                onChange={(e) => setResponseNotes(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6]"
                rows="3"
                placeholder="Enter any additional notes..."
              />
            </div>

            <div className="flex gap-3">
              <button
                onClick={submitAction}
                disabled={submitting}
                className={`flex-1 px-4 py-2 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${
                  actionType === 'approved' 
                    ? 'bg-green-600 hover:bg-green-700' 
                    : 'bg-red-600 hover:bg-red-700'
                }`}
              >
                {submitting ? 'Processing...' : (actionType === 'approved' ? 'Approve' : 'Reject')}
              </button>
              <button
                onClick={() => setShowModal(false)}
                className="flex-1 px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
              >
                Cancel
              </button>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
};

export default LabRequests;
