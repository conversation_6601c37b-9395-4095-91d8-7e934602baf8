import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { motion } from 'framer-motion';
import { FaUniversity, FaEdit, FaTrash, FaPlus } from 'react-icons/fa';
import Navbar from '../student/Navbar';
import Loader from '../components/Loader';
import ConfirmModal from '../components/ConfirmModal';
import UniversityDetailsModal from '../components/UniversityDetailsModal';
import { useAuth } from '../context/AuthContext';
import SuperAdminSidebar from './SuperAdminSidebar';

const Universities = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [universities, setUniversities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showUniversityModal, setShowUniversityModal] = useState(false);
  const [selectedUniversityId, setSelectedUniversityId] = useState(null);
  const [selectedUniversity, setSelectedUniversity] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    universityId: '',
    name: { en: '', ar: '' },
    description: { en: '', ar: '' },
    dentistryInfo: { en: '', ar: '' },
    facilities: { en: '', ar: '' },
    program: { en: '', ar: '' },
    dentistryServices: [{ en: '', ar: '' }],
    address: {
      street: { en: '', ar: '' },
      city: { en: '', ar: '' },
      country: { en: '', ar: '' },
      postalCode: '',
    },
    contactInfo: {
      phone: '',
      email: '',
      website: '',
    },
    slotBeginDate: '',
    slotEndDate: '',
    slotDuration: 120, // Default slot duration in minutes
    availableSlots: ['09:00', '11:30', '14:00'], // Default available time slots
    holidays: ['Friday', 'Sunday'], // Default holidays
    logo: '',
    image: '',
    mapUrl: '',
  });
  const navigate = useNavigate();
  const { user, token } = useAuth();

  useEffect(() => {
    const fetchUniversities = async () => {
      if (!user || !token) {
        setError('Please log in to view universities.');
        setLoading(false);
        return;
      }

      try {
        const config = { headers: { Authorization: `Bearer ${token}` } };
        const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/universities`, config);
        console.log('Universities response:', response.data); // Debug API response
        setUniversities(response.data || []);
      } catch (err) {
        console.error('Fetch error:', err.response?.data || err.message);
        const errorMessage = err.response?.status === 401
          ? 'Unauthorized. Please log in again.'
          : err.response?.data?.message || 'Failed to load universities';
        setError(errorMessage);
        if (err.response?.status === 401) {
          navigate('/login');
        }
      } finally {
        setLoading(false);
      }
    };
    fetchUniversities();
  }, [user, token, navigate]);

  const handleRowClick = async (universityId) => {
    try {
      const config = { headers: { Authorization: `Bearer ${token}` } };
      const response = await axios.get(`${process.env.REACT_APP_API_URL}/api/universities/${universityId}`, config);
      setSelectedUniversity(response.data);
      setShowDetailsModal(true);
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to load university details');
    }
  };

  const handleEditClick = (e, university) => {
    e.stopPropagation();
    console.log('Editing university:', university); // Debug university object

    // Determine slot duration from university data
    let slotDuration = 120; // Default value
    if (university.slotDuration) {
      // If slotDuration is directly available
      slotDuration = university.slotDuration;
    } else if (Array.isArray(university.timeSlots) && university.timeSlots.length > 0 && university.timeSlots[0].duration) {
      // Extract from the first time slot if available
      slotDuration = university.timeSlots[0].duration;
    }

    // Extract available time slots
    let availableSlots = ['09:00', '11:30', '14:00']; // Default values
    if (Array.isArray(university.timeSlots) && university.timeSlots.length > 0) {
      // Get unique time slots
      availableSlots = [...new Set(university.timeSlots.map(slot => slot.time))];
    }

    setFormData({
      universityId: university.universityId || '',
      name: {
        en: university.name?.en || '',
        ar: university.name?.ar || '',
      },
      description: {
        en: university.description?.en || '',
        ar: university.description?.ar || '',
      },
      dentistryInfo: {
        en: university.dentistryInfo?.en || '',
        ar: university.dentistryInfo?.ar || '',
      },
      facilities: {
        en: university.facilities?.en || '',
        ar: university.facilities?.ar || '',
      },
      program: {
        en: university.program?.en || '',
        ar: university.program?.ar || '',
      },
      dentistryServices: Array.isArray(university.dentistryServices) && university.dentistryServices.length > 0
        ? university.dentistryServices.map(service => ({
            en: service?.en || '',
            ar: service?.ar || '',
          }))
        : [{ en: '', ar: '' }],
      address: {
        street: {
          en: university.address?.street?.en || '',
          ar: university.address?.street?.ar || '',
        },
        city: {
          en: university.address?.city?.en || '',
          ar: university.address?.city?.ar || '',
        },
        country: {
          en: university.address?.country?.en || '',
          ar: university.address?.country?.ar || '',
        },
        postalCode: university.address?.postalCode || '',
      },
      contactInfo: {
        phone: university.contactInfo?.phone || '',
        email: university.contactInfo?.email || '',
        website: university.contactInfo?.website || '',
      },
      slotBeginDate: university.slotBeginDate
        ? new Date(university.slotBeginDate).toISOString().split('T')[0]
        : '',
      slotEndDate: university.slotEndDate
        ? new Date(university.slotEndDate).toISOString().split('T')[0]
        : '',
      slotDuration: slotDuration,
      availableSlots: availableSlots,
      holidays: Array.isArray(university.holidays) ? university.holidays : ['Friday', 'Sunday'],
      logo: university.logo || '',
      image: university.image || '',
      mapUrl: university.mapUrl || '',
    });

    setIsEditing(true);
    setShowUniversityModal(true);
  };

  const handleAddUniversity = () => {
    setFormData({
      universityId: '',
      name: { en: '', ar: '' },
      description: { en: '', ar: '' },
      dentistryInfo: { en: '', ar: '' },
      facilities: { en: '', ar: '' },
      program: { en: '', ar: '' },
      dentistryServices: [{ en: '', ar: '' }],
      address: {
        street: { en: '', ar: '' },
        city: { en: '', ar: '' },
        country: { en: '', ar: '' },
        postalCode: '',
      },
      contactInfo: {
        phone: '',
        email: '',
        website: '',
      },
      slotBeginDate: '',
      slotEndDate: '',
      slotDuration: 120, // Default slot duration in minutes
      availableSlots: ['09:00', '11:30', '14:00'], // Default available time slots
      holidays: ['Friday', 'Sunday'], // Default holidays
      logo: '',
      image: '',
      mapUrl: '',
    });
    setIsEditing(false);
    setShowUniversityModal(true);
  };

  const handleDeleteClick = (e, universityId) => {
    e.stopPropagation();
    setSelectedUniversityId(universityId);
    setShowConfirmModal(true);
  };

  const handleConfirmDelete = async () => {
    try {
      const config = { headers: { Authorization: `Bearer ${token}` } };
      await axios.delete(`${process.env.REACT_APP_API_URL}/api/universities/${selectedUniversityId}`, config);
      setUniversities(universities.filter((u) => u.universityId !== selectedUniversityId));
      setShowConfirmModal(false);
      setSelectedUniversityId(null);
      setSuccess('University deleted successfully!');
      setTimeout(() => setSuccess(''), 3000);
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to delete university');
      setShowConfirmModal(false);
    }
  };

  const handleInputChange = (e, field, subField, lang) => {
    const value = e.target.value;
    setFormData((prev) => {
      // Handle address fields with language parameters (address.street.en, address.city.ar, etc.)
      if (field === 'address' && subField && lang) {
        return {
          ...prev,
          address: {
            ...prev.address,
            [subField]: {
              ...prev.address[subField],
              [lang]: value
            }
          }
        };
      }
      // Handle regular bilingual fields (name.en, description.ar, etc.)
      else if (lang) {
        return {
          ...prev,
          [field]: {
            ...prev[field],
            [lang]: value,
          },
        };
      }
      // Handle object fields with subfields (contactInfo.phone, contactInfo.email, etc.)
      else if (subField) {
        return {
          ...prev,
          [field]: {
            ...prev[field],
            [subField]: value,
          },
        };
      }
      // Handle simple fields (universityId, slotBeginDate, etc.)
      return {
        ...prev,
        [field]: value,
      };
    });
  };

  const handleServiceChange = (index, lang, value) => {
    setFormData((prev) => {
      const newServices = [...prev.dentistryServices];
      newServices[index] = { ...newServices[index], [lang]: value };
      return { ...prev, dentistryServices: newServices };
    });
  };

  const addService = () => {
    setFormData((prev) => ({
      ...prev,
      dentistryServices: [...prev.dentistryServices, { en: '', ar: '' }],
    }));
  };

  const removeService = (index) => {
    setFormData((prev) => ({
      ...prev,
      dentistryServices: prev.dentistryServices.filter((_, i) => i !== index),
    }));
  };

  const handleSlotDurationChange = (e) => {
    setFormData((prev) => ({
      ...prev,
      slotDuration: parseInt(e.target.value),
    }));
  };

  const handleTimeSlotChange = (index, value) => {
    setFormData((prev) => {
      const newSlots = [...prev.availableSlots];
      newSlots[index] = value;
      return { ...prev, availableSlots: newSlots };
    });
  };

  const addTimeSlot = () => {
    setFormData((prev) => ({
      ...prev,
      availableSlots: [...prev.availableSlots, '09:00'],
    }));
  };

  const removeTimeSlot = (index) => {
    setFormData((prev) => ({
      ...prev,
      availableSlots: prev.availableSlots.filter((_, i) => i !== index),
    }));
  };

  const handleHolidayChange = (day) => {
    setFormData((prev) => {
      const currentHolidays = [...prev.holidays];
      if (currentHolidays.includes(day)) {
        // Remove the day if it's already in the holidays array
        return {
          ...prev,
          holidays: currentHolidays.filter(holiday => holiday !== day)
        };
      } else {
        // Add the day if it's not in the holidays array
        return {
          ...prev,
          holidays: [...currentHolidays, day]
        };
      }
    });
  };

  const handleUniversitySubmit = async (e) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    // Validate required fields
    if (!formData.universityId.trim()) {
      setError('University ID is required');
      return;
    }

    if (!formData.name.en.trim() || !formData.name.ar.trim()) {
      setError('University name is required in both English and Arabic');
      return;
    }

    // Validate address fields
    if (!formData.address.street.en.trim() || !formData.address.street.ar.trim()) {
      setError('Street address is required in both English and Arabic');
      return;
    }

    if (!formData.address.city.en.trim() || !formData.address.city.ar.trim()) {
      setError('City is required in both English and Arabic');
      return;
    }

    if (!formData.address.country.en.trim() || !formData.address.country.ar.trim()) {
      setError('Country is required in both English and Arabic');
      return;
    }

    if (!formData.contactInfo.phone.trim() || !formData.contactInfo.email.trim()) {
      setError('Contact information (phone and email) is required');
      return;
    }

    if (!formData.slotBeginDate || !formData.slotEndDate) {
      setError('Slot begin and end dates are required');
      return;
    }

    if (!formData.slotDuration || formData.slotDuration < 30) {
      setError('Valid slot duration is required (minimum 30 minutes)');
      return;
    }

    if (!formData.availableSlots || formData.availableSlots.length === 0) {
      setError('At least one available time slot is required');
      return;
    }

    // Prepare form data with default values for required fields
    const formToSubmit = { ...formData };

    // Set default values for empty bilingual fields
    ['description', 'dentistryInfo', 'facilities', 'program'].forEach(field => {
      if (!formToSubmit[field].en.trim() || !formToSubmit[field].ar.trim()) {
        formToSubmit[field] = {
          en: formToSubmit[field].en.trim() || 'Not provided',
          ar: formToSubmit[field].ar.trim() || 'غير متوفر'
        };
      }
    });

    // Ensure address fields have values
    ['street', 'city', 'country'].forEach(field => {
      if (!formToSubmit.address[field].en.trim() || !formToSubmit.address[field].ar.trim()) {
        formToSubmit.address[field] = {
          en: formToSubmit.address[field].en.trim() || 'Not provided',
          ar: formToSubmit.address[field].ar.trim() || 'غير متوفر'
        };
      }
    });

    // Ensure dentistry services have values
    if (formToSubmit.dentistryServices.length === 0) {
      formToSubmit.dentistryServices = [{ en: 'General Dentistry', ar: 'طب الأسنان العام' }];
    } else {
      formToSubmit.dentistryServices = formToSubmit.dentistryServices.map(service => ({
        en: service.en.trim() || 'General Dentistry',
        ar: service.ar.trim() || 'طب الأسنان العام'
      }));
    }

    try {
      const config = { headers: { Authorization: `Bearer ${token}` } };
      let response;

      console.log('Submitting university data:', formToSubmit);

      if (isEditing) {
        response = await axios.put(
          `${process.env.REACT_APP_API_URL}/api/universities/${formToSubmit.universityId}`,
          formToSubmit,
          config
        );
        setUniversities(universities.map(u => u.universityId === formToSubmit.universityId ? response.data : u));
        setSuccess('University updated successfully!');
      } else {
        response = await axios.post(`${process.env.REACT_APP_API_URL}/api/universities`, formToSubmit, config);
        setUniversities([...universities, response.data]);
        setSuccess('University created successfully!');
      }
      setShowUniversityModal(false);
      setTimeout(() => setSuccess(''), 3000);
    } catch (err) {
      console.error('University submission error:', err);

      // Create a more detailed error message
      let errorMessage = 'Failed to save university. ';

      if (err.response?.data?.error) {
        // If the backend sends a detailed error message
        errorMessage += err.response.data.error;
      } else if (err.response?.data?.message) {
        // If the backend sends a simple message
        errorMessage += err.response.data.message;
      } else if (err.message) {
        // If there's a general error message
        errorMessage += err.message;
      }

      setError(errorMessage);
    }
  };

  const container = {
    hidden: { opacity: 0 },
    show: { opacity: 1, transition: { staggerChildren: 0.1 } },
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 },
  };

  if (loading) {
    return <Loader />;
  }

  return (
    <div className="flex h-screen bg-gray-50">
      <SuperAdminSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />

      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />

        <main className="flex-1 overflow-y-auto p-6 bg-gradient-to-br from-blue-50 to-white">
          <div className="max-w-7xl mx-auto">
            {error && (
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm"
              >
                <div className="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500 mr-3" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  <p className="text-red-700 font-medium">{error}</p>
                </div>
              </motion.div>
            )}
            {success && (
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-6 p-4 bg-green-50 border-l-4 border-green-500 rounded-lg shadow-sm"
              >
                <div className="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mr-3" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <p className="text-green-700 font-medium">{success}</p>
                </div>
              </motion.div>
            )}

            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
                <div>
                  <h1 className="text-3xl md:text-4xl font-bold text-[#0077B6] mb-1">Universities</h1>
                  <p className="text-[#333333]">Manage university information and settings</p>
                </div>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={handleAddUniversity}
                  className="w-full md:w-auto bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white px-6 py-3 rounded-full font-medium transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center"
                >
                  <FaPlus className="h-5 w-5 mr-2" />
                  Add University
                </motion.button>
              </div>

              <motion.div
                variants={container}
                initial="hidden"
                whileInView="show"
                viewport={{ once: true }}
                className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 overflow-hidden"
              >
                <div className="p-6">
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name (EN)</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name (AR)</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {universities.length === 0 ? (
                          <tr>
                            <td colSpan="6" className="px-6 py-8 text-center">
                              <div className="flex flex-col items-center justify-center">
                                <FaUniversity className="h-12 w-12 text-gray-400 mb-4" />
                                <h3 className="text-lg font-medium text-gray-900">No universities found</h3>
                                <p className="mt-1 text-gray-500">Add a university using the button above.</p>
                              </div>
                            </td>
                          </tr>
                        ) : (
                          universities.map((university) => (
                            <motion.tr
                              key={university.universityId}
                              variants={item}
                              onClick={() => handleRowClick(university.universityId)}
                              className="hover:bg-gray-50 cursor-pointer"
                            >
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{university.universityId || 'N/A'}</td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{university.name?.en || 'N/A'}</td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{university.name?.ar || 'N/A'}</td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{university.contactInfo?.phone || 'N/A'}</td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{university.contactInfo?.email || 'N/A'}</td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 flex space-x-2">
                                <button
                                  onClick={(e) => handleEditClick(e, university)}
                                  className="text-[#0077B6] hover:text-[#20B2AA]"
                                  title="Edit"
                                >
                                  <FaEdit className="h-5 w-5" />
                                </button>
                                <button
                                  onClick={(e) => handleDeleteClick(e, university.universityId)}
                                  className="text-red-600 hover:text-red-800"
                                  title="Delete"
                                >
                                  <FaTrash className="h-5 w-5" />
                                </button>
                              </td>
                            </motion.tr>
                          ))
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          </div>
        </main>
      </div>

      <ConfirmModal
        isOpen={showConfirmModal}
        onClose={() => setShowConfirmModal(false)}
        onConfirm={handleConfirmDelete}
        title="Confirm Delete"
        message="Are you sure you want to delete this university? This action cannot be undone."
      />

      <UniversityDetailsModal
        isOpen={showDetailsModal}
        onClose={() => setShowDetailsModal(false)}
        university={selectedUniversity}
      />

      {showUniversityModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className="bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto"
          >
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold text-[#0077B6]">{isEditing ? 'Edit University' : 'Add University'}</h2>
                <button onClick={() => setShowUniversityModal(false)} className="text-gray-400 hover:text-gray-500">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              <form onSubmit={handleUniversitySubmit} className="space-y-6">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">University ID*</label>
                    <input
                      type="text"
                      value={formData.universityId}
                      onChange={(e) => handleInputChange(e, 'universityId')}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                      required
                      disabled={isEditing}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Name (English)*</label>
                    <input
                      type="text"
                      value={formData.name.en}
                      onChange={(e) => handleInputChange(e, 'name', null, 'en')}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Name (Arabic)*</label>
                    <input
                      type="text"
                      value={formData.name.ar}
                      onChange={(e) => handleInputChange(e, 'name', null, 'ar')}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Phone*</label>
                    <input
                      type="tel"
                      value={formData.contactInfo.phone}
                      onChange={(e) => handleInputChange(e, 'contactInfo', 'phone')}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Email*</label>
                    <input
                      type="email"
                      value={formData.contactInfo.email}
                      onChange={(e) => handleInputChange(e, 'contactInfo', 'email')}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Website</label>
                    <input
                      type="url"
                      value={formData.contactInfo.website}
                      onChange={(e) => handleInputChange(e, 'contactInfo', 'website')}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#20B2AA] focus:border-[#20B2AA]"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Street (English)*</label>
                    <input
                      type="text"
                      value={formData.address.street.en}
                      onChange={(e) => handleInputChange(e, 'address', 'street', 'en')}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Street (Arabic)*</label>
                    <input
                      type="text"
                      value={formData.address.street.ar}
                      onChange={(e) => handleInputChange(e, 'address', 'street', 'ar')}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">City (English)*</label>
                    <input
                      type="text"
                      value={formData.address.city.en}
                      onChange={(e) => handleInputChange(e, 'address', 'city', 'en')}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">City (Arabic)*</label>
                    <input
                      type="text"
                      value={formData.address.city.ar}
                      onChange={(e) => handleInputChange(e, 'address', 'city', 'ar')}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Country (English)*</label>
                    <input
                      type="text"
                      value={formData.address.country.en}
                      onChange={(e) => handleInputChange(e, 'address', 'country', 'en')}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Country (Arabic)*</label>
                    <input
                      type="text"
                      value={formData.address.country.ar}
                      onChange={(e) => handleInputChange(e, 'address', 'country', 'ar')}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Postal Code</label>
                    <input
                      type="text"
                      value={formData.address.postalCode}
                      onChange={(e) => handleInputChange(e, 'address', 'postalCode')}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Slot Begin Date*</label>
                    <input
                      type="date"
                      value={formData.slotBeginDate}
                      onChange={(e) => handleInputChange(e, 'slotBeginDate')}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Slot End Date*</label>
                    <input
                      type="date"
                      value={formData.slotEndDate}
                      onChange={(e) => handleInputChange(e, 'slotEndDate')}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Slot Settings</label>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-2 p-4 border border-gray-200 rounded-lg">
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">Slot Duration (minutes)*</label>
                      <input
                        type="number"
                        value={formData.slotDuration}
                        onChange={handleSlotDurationChange}
                        min="30"
                        step="30"
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">Available Time Slots*</label>
                      <div className="space-y-2">
                        {formData.availableSlots.map((slot, index) => (
                          <div key={index} className="flex items-center space-x-2">
                            <input
                              type="time"
                              value={slot}
                              onChange={(e) => handleTimeSlotChange(index, e.target.value)}
                              className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              required
                            />
                            <button
                              type="button"
                              onClick={() => removeTimeSlot(index)}
                              className="text-red-500 hover:text-red-700"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                              </svg>
                            </button>
                          </div>
                        ))}
                        <button
                          type="button"
                          onClick={addTimeSlot}
                          className="px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors w-full"
                        >
                          + Add Time Slot
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Holidays</label>
                  <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 mt-2 p-4 border border-gray-200 rounded-lg">
                    {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'].map((day) => (
                      <div key={day} className="flex items-center">
                        <input
                          type="checkbox"
                          id={`holiday-${day}`}
                          checked={formData.holidays.includes(day)}
                          onChange={() => handleHolidayChange(day)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label htmlFor={`holiday-${day}`} className="ml-2 block text-sm text-gray-700">
                          {day}
                        </label>
                      </div>
                    ))}
                  </div>
                  <p className="mt-1 text-sm text-gray-500">Select days when the university is closed.</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Description (English)</label>
                  <textarea
                    value={formData.description.en}
                    onChange={(e) => handleInputChange(e, 'description', null, 'en')}
                    rows="3"
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Description (Arabic)</label>
                  <textarea
                    value={formData.description.ar}
                    onChange={(e) => handleInputChange(e, 'description', null, 'ar')}
                    rows="3"
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Dentistry Info (English)</label>
                  <textarea
                    value={formData.dentistryInfo.en}
                    onChange={(e) => handleInputChange(e, 'dentistryInfo', null, 'en')}
                    rows="3"
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Dentistry Info (Arabic)</label>
                  <textarea
                    value={formData.dentistryInfo.ar}
                    onChange={(e) => handleInputChange(e, 'dentistryInfo', null, 'ar')}
                    rows="3"
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Facilities (English)</label>
                  <textarea
                    value={formData.facilities.en}
                    onChange={(e) => handleInputChange(e, 'facilities', null, 'en')}
                    rows="3"
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Facilities (Arabic)</label>
                  <textarea
                    value={formData.facilities.ar}
                    onChange={(e) => handleInputChange(e, 'facilities', null, 'ar')}
                    rows="3"
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Program (English)</label>
                  <textarea
                    value={formData.program.en}
                    onChange={(e) => handleInputChange(e, 'program', null, 'en')}
                    rows="3"
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Program (Arabic)</label>
                  <textarea
                    value={formData.program.ar}
                    onChange={(e) => handleInputChange(e, 'program', null, 'ar')}
                    rows="3"
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-700 mb-2">Dentistry Services</h3>
                  {formData.dentistryServices.map((service, index) => (
                    <div key={index} className="mb-4 p-4 border border-gray-200 rounded-md">
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Service (English)*</label>
                          <input
                            type="text"
                            value={service.en}
                            onChange={(e) => handleServiceChange(index, 'en', e.target.value)}
                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            required
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Service (Arabic)*</label>
                          <input
                            type="text"
                            value={service.ar}
                            onChange={(e) => handleServiceChange(index, 'ar', e.target.value)}
                            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            required
                          />
                        </div>
                      </div>
                      {formData.dentistryServices.length > 1 && (
                        <button
                          type="button"
                          onClick={() => removeService(index)}
                          className="mt-2 text-red-600 hover:text-red-800 text-sm font-medium"
                        >
                          Remove Service
                        </button>
                      )}
                    </div>
                  ))}
                  <button
                    type="button"
                    onClick={addService}
                    className="mt-2 text-blue-600 hover:text-blue-800 text-sm font-medium"
                  >
                    Add Another Service
                  </button>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Logo URL</label>
                    <input
                      type="url"
                      value={formData.logo}
                      onChange={(e) => handleInputChange(e, 'logo')}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Image URL</label>
                    <input
                      type="url"
                      value={formData.image}
                      onChange={(e) => handleInputChange(e, 'image')}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Map URL</label>
                    <input
                      type="url"
                      value={formData.mapUrl}
                      onChange={(e) => handleInputChange(e, 'mapUrl')}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
                <div className="flex justify-end space-x-4 pt-4">
                  <motion.button
                    type="button"
                    onClick={() => setShowUniversityModal(false)}
                    className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 font-medium transition-colors"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    Cancel
                  </motion.button>
                  <motion.button
                    type="submit"
                    className="px-6 py-2 bg-gradient-to-r from-blue-500 to-blue-700 text-white rounded-lg hover:from-blue-600 hover:to-blue-800 font-medium transition-colors shadow-md hover:shadow-lg"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    {isEditing ? 'Update University' : 'Add University'}
                  </motion.button>
                </div>
              </form>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
};

export default Universities;