import { useState } from 'react';
import { motion } from 'framer-motion';
import MedicalTab from './MedicalTab';
import { generateSheetPDF } from '../utils/pdfUtils';

const OperativeSheet = ({ initialData, onSave }) => {
  const defaultFormData = {
    dentalHistory: '',
    dentalExamination: {
      feelingPain: '',
      localized: '',
      spontaneous: '',
      durationAfterStimulus: '',
      awakenAtNight: '',
      severePain: ''
    }
  };

  const mergedData = initialData && Object.keys(initialData).length > 0
    ? { ...defaultFormData, ...initialData }
    : defaultFormData;

  const [formData, setFormData] = useState(mergedData);
  const [errors, setErrors] = useState({});
  const [activeTab, setActiveTab] = useState('medical');

  // Modified validation to make fields optional
  const validateForm = () => {
    // Since all fields are now optional, we're just returning true
    // You can add custom validation logic here if needed for specific fields
    return true;
  };

  const handleChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value
    }));
    setErrors((prev) => ({ ...prev, [field]: '' }));
  };

  const handleNestedChange = (section, subsection, field, value) => {
    if (subsection) {
      // Handle nested subsection
      setFormData((prev) => ({
        ...prev,
        [section]: {
          ...prev[section],
          [subsection]: {
            ...prev[section][subsection],
            [field]: value
          }
        }
      }));
      setErrors((prev) => ({ ...prev, [`${section}.${subsection}.${field}`]: '' }));
    } else {
      // Handle direct section fields
      setFormData((prev) => ({
        ...prev,
        [section]: {
          ...prev[section],
          [field]: value
        }
      }));
      setErrors((prev) => ({ ...prev, [`${section}.${field}`]: '' }));
    }
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      alert('Please fill all required fields.');
      return;
    }

    // Create diagnosis and treatment plan strings
    const diagnosis = `Operative treatment needed based on dental examination: ${
      formData.dentalExamination.feelingPain === 'Yes' ? 'Patient reports pain' : 'No pain reported'
    }`;

    const treatmentPlan = `Operative treatment plan based on dental history and examination`;

    // Call the onSave function from props
    if (onSave) {
      const success = await onSave(formData, diagnosis, treatmentPlan, '');
      if (!success) {
        alert('Failed to save sheet. Please try again.');
      }
    } else {
      alert('Form submitted successfully!');
    }
  };

  const handleDownloadPDF = async () => {
    try {
      // Create a mock sheet object for PDF generation
      const mockSheet = {
        type: 'Operative',
        createdAt: new Date().toISOString(),
        details: {
          diagnosis: `Operative treatment needed based on dental examination: ${
            formData.dentalExamination.feelingPain === 'Yes' ? 'Patient reports pain' : 'No pain reported'
          }`,
          treatmentPlan: 'Operative treatment plan based on dental history and examination',
          specificData: formData
        }
      };

      await generateSheetPDF(mockSheet, 'Current_Patient');
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Failed to generate PDF. Please try again.');
    }
  };

  const renderRadioButtons = (label, value, onChange, options, error, required = false) => (
    <div className="mb-5">
      <label className="block text-base font-medium text-gray-700 mb-2">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
        {options.map((option) => (
          <div
            key={option}
            className={`flex items-center p-2 rounded-lg border transition-all ${
              value === option
                ? 'bg-[rgba(40,167,69,0.1)] border-[#28A745]'
                : 'bg-white border-gray-200 hover:bg-gray-50'
            }`}
          >
            <input
              type="radio"
              id={`${label.replace(/\s+/g, '')}-${option}`}
              name={label.replace(/\s+/g, '')}
              value={option}
              checked={value === option}
              onChange={onChange}
              className={`h-4 w-4 ${value === option ? 'text-[#28A745] focus:ring-[#28A745]' : 'text-[#0077B6] focus:ring-[#0077B6]'} border-gray-300`}
            />
            <label
              htmlFor={`${label.replace(/\s+/g, '')}-${option}`}
              className={`ml-2 text-sm cursor-pointer w-full ${value === option ? 'font-medium text-[#28A745]' : 'text-gray-700'}`}
            >
              {option}
            </label>
          </div>
        ))}
      </div>
      {error && <p className="text-red-500 text-xs mt-1">{error}</p>}
    </div>
  );

  const renderTextArea = (label, value, onChange, error, required = false) => (
    <div className="mb-4">
      <label className="block text-base font-medium text-gray-700 mb-1">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      <textarea
        value={value}
        onChange={onChange}
        className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#0077B6] focus:border-[#0077B6] transition-all ${error ? 'border-red-500' : 'border-gray-300'}`}
        rows="4"
      />
      {error && <p className="text-red-500 text-xs mt-1">{error}</p>}
    </div>
  );

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="w-full mx-auto mt-8 p-6 bg-white rounded-xl shadow-md border border-[rgba(0,119,182,0.1)]"
    >
      <h3 className="text-2xl font-bold text-[#0077B6] mb-6">Operative Sheet</h3>

      {/* Tabs */}
      <div className="flex border-b border-[rgba(0,119,182,0.1)] mb-6 overflow-x-auto">
        <button
          className={`px-4 py-2 font-medium transition-all ${activeTab === 'medical' ? 'border-b-2 border-[#0077B6] text-[#0077B6] bg-[rgba(0,119,182,0.05)]' : 'text-gray-500 hover:text-[#0077B6] hover:bg-[rgba(0,119,182,0.03)]'}`}
          onClick={() => setActiveTab('medical')}
        >
          Medical
        </button>
        <button
          className={`px-4 py-2 font-medium transition-all ${activeTab === 'dental' ? 'border-b-2 border-[#0077B6] text-[#0077B6] bg-[rgba(0,119,182,0.05)]' : 'text-gray-500 hover:text-[#0077B6] hover:bg-[rgba(0,119,182,0.03)]'}`}
          onClick={() => setActiveTab('dental')}
        >
          Dental
        </button>
      </div>

      {activeTab === 'medical' && (
        <div>
          <MedicalTab onSave={(medicalInfo) => {
            console.log('Medical info updated:', medicalInfo);
            // You can update the form data here if needed
          }} />
        </div>
      )}

      {activeTab === 'dental' && (
        <div>
          <div className="mb-6 bg-white p-5 rounded-lg border border-gray-200 shadow-sm">
            <h4 className="text-lg font-semibold text-[#0077B6] mb-3">Dental History</h4>
            {renderTextArea(
              'Dental History',
              formData.dentalHistory,
              (e) => handleChange('dentalHistory', e.target.value),
              errors['dentalHistory']
            )}
          </div>

          <div className="mb-6 bg-white p-5 rounded-lg border border-gray-200 shadow-sm">
            <h4 className="text-lg font-semibold text-[#0077B6] mb-3">Dental Examination</h4>
            {renderRadioButtons(
              'Feeling Pain',
              formData.dentalExamination.feelingPain,
              (e) => handleNestedChange('dentalExamination', null, 'feelingPain', e.target.value),
              ['Yes', 'No'],
              errors['dentalExamination.feelingPain']
            )}
            {renderRadioButtons(
              'Localized',
              formData.dentalExamination.localized,
              (e) => handleNestedChange('dentalExamination', null, 'localized', e.target.value),
              ['Yes', 'No'],
              errors['dentalExamination.localized']
            )}
            {renderRadioButtons(
              'Spontaneous',
              formData.dentalExamination.spontaneous,
              (e) => handleNestedChange('dentalExamination', null, 'spontaneous', e.target.value),
              ['Yes', 'No'],
              errors['dentalExamination.spontaneous']
            )}
            {renderRadioButtons(
              'Duration after Stimulus',
              formData.dentalExamination.durationAfterStimulus,
              (e) => handleNestedChange('dentalExamination', null, 'durationAfterStimulus', e.target.value),
              ['Yes', 'No'],
              errors['dentalExamination.durationAfterStimulus']
            )}
            {renderRadioButtons(
              'Awaken at Night',
              formData.dentalExamination.awakenAtNight,
              (e) => handleNestedChange('dentalExamination', null, 'awakenAtNight', e.target.value),
              ['Yes', 'No'],
              errors['dentalExamination.awakenAtNight']
            )}
            {renderRadioButtons(
              'Severe Pain',
              formData.dentalExamination.severePain,
              (e) => handleNestedChange('dentalExamination', null, 'severePain', e.target.value),
              ['Yes', 'No'],
              errors['dentalExamination.severePain']
            )}
          </div>
        </div>
      )}

      <div className="mt-8 flex justify-end gap-4">
        <button
          onClick={handleDownloadPDF}
          className="px-6 py-2 bg-gradient-to-r from-[#0077B6] to-[#20B2AA] text-white font-medium rounded-lg hover:shadow-lg transition-all duration-300 flex items-center"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 mr-2"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
          Download PDF
        </button>
        <button
          onClick={handleSubmit}
          className="px-6 py-2 bg-[#0077B6] text-white font-medium rounded-lg hover:bg-[#005f92] transition-all shadow-sm"
        >
          Save Sheet
        </button>
      </div>
    </motion.div>
  );
};

export default OperativeSheet;