import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { useAuth } from '../context/AuthContext';
import Navbar from '../student/Navbar';
import AdminSidebar from './AdminSidebar';
import Loader from '../components/Loader';
import { motion, AnimatePresence } from 'framer-motion';
import { FaNewspaper, FaSync, FaGlobeAmericas, FaUniversity, FaCalendarAlt, FaInfoCircle } from 'react-icons/fa';

// Website color palette
const websiteColorPalette = {
  primary: '#0077B6',
  secondary: '#20B2AA',
  background: '#FFFFFF',
  text: '#333333',
  accent: '#28A745'
};

const News = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [news, setNews] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState('');
  const [showNoNewsInfo, setShowNoNewsInfo] = useState(false);
  const navigate = useNavigate();
  const { user, token } = useAuth();

  const fetchNews = async () => {
    if (!user || !token) {
      setError('Please log in to view news.');
      setLoading(false);
      return;
    }

    try {
      setRefreshing(true);
      const config = { headers: { Authorization: `Bearer ${token}` } };
      const response = await axios.get(
        'http://localhost:5000/api/news/admin',
        config
      );

      setNews(response.data || []);
      setError('');

      // If no news, show info message but don't treat it as an error
      if (response.data.length === 0) {
        setShowNoNewsInfo(true);
      } else {
        setShowNoNewsInfo(false);
      }
    } catch (err) {
      console.error('Fetch error:', err.response?.data || err.message);
      const errorMessage =
        err.response?.status === 404
          ? 'News endpoint not found.'
          : err.response?.status === 401
          ? 'Unauthorized. Please log in again.'
          : err.response?.status === 403
          ? 'Access denied. Insufficient role permissions.'
          : err.response?.data?.message || 'Failed to load news';
      setError(errorMessage);
      if (err.response?.status === 401) navigate('/login');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchNews();
  }, [user, token, navigate]);

  const handleRefresh = () => {
    fetchNews();
  };

  const formatDate = (dateString) => {
    const options = {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Helper function to get display text from title/content (handles both string and object formats)
  const getDisplayText = (text) => {
    if (typeof text === 'string') {
      return text;
    } else if (text && typeof text === 'object' && text.en) {
      return text.en; // Default to English
    }
    return 'No content available';
  };

  const container = {
    hidden: { opacity: 0 },
    show: { opacity: 1, transition: { staggerChildren: 0.1 } },
  };

  // Animation variant for individual news items
  const itemVariant = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 },
  };

  if (loading) return <Loader />;

  return (
    <div className="flex h-screen bg-gray-50">
      <AdminSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />
      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />
        <main className="flex-1 overflow-y-auto p-4 sm:p-6 bg-gradient-to-br from-blue-50 to-white">
          <div className="max-w-7xl mx-auto">
            <AnimatePresence>
              {error && (
                <motion.div
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  className="mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm"
                >
                  <div className="flex items-center">
                    <svg className="w-5 h-5 text-red-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                    <p className="text-red-700 font-medium">{error}</p>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.5 }}>
              <div className="mb-6 sm:mb-8 flex flex-col sm:flex-row justify-between items-start sm:items-end gap-4">
                <div>
                  <h1 className={`text-2xl sm:text-3xl md:text-4xl font-bold text-[${websiteColorPalette.primary}] mb-1`}>News & Updates</h1>
                  <p className={`text-[${websiteColorPalette.text}]`}>Stay updated with university news</p>
                </div>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={handleRefresh}
                  disabled={refreshing}
                  className={`px-3 sm:px-4 py-2 rounded-lg flex items-center text-white font-medium text-xs sm:text-sm ${
                    refreshing ? 'bg-blue-400 cursor-not-allowed' : `bg-[${websiteColorPalette.primary}] hover:bg-blue-700`
                  }`}
                >
                  <FaSync className={`mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                  {refreshing ? 'Refreshing...' : 'Refresh'}
                </motion.button>
              </div>

              <motion.div
                variants={container}
                initial="hidden"
                whileInView="show"
                viewport={{ once: true }}
                className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 overflow-hidden"
              >
                <div className="p-4 sm:p-6">
                  <h2 className={`text-lg sm:text-xl font-bold text-[${websiteColorPalette.primary}] mb-4 sm:mb-6 flex items-center`}>
                    <FaNewspaper className="h-4 w-4 sm:h-5 sm:w-5 mr-2" />
                    Latest News
                  </h2>

                  {showNoNewsInfo && (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className={`mb-4 sm:mb-6 p-3 sm:p-4 bg-blue-50 border-l-4 border-[${websiteColorPalette.primary}] rounded-lg`}
                    >
                      <div className="flex items-start">
                        <FaInfoCircle className={`w-4 h-4 sm:w-5 sm:h-5 text-[${websiteColorPalette.primary}] mr-3 mt-0.5`} />
                        <div>
                          <p className={`text-[${websiteColorPalette.primary}] font-medium text-sm`}>No news available yet</p>
                          <p className={`text-xs sm:text-sm text-[${websiteColorPalette.text}] mt-1`}>
                            News will appear here when the superadmin sends updates to your university.
                            You can check back later or use the refresh button to check for new updates.
                          </p>
                        </div>
                      </div>
                    </motion.div>
                  )}

                  <div className="space-y-4 sm:space-y-6">
                    {news.length === 0 ? (
                      <div className="flex flex-col items-center justify-center py-8 sm:py-12">
                        <div className="bg-blue-50 rounded-full p-4 sm:p-6 mb-4">
                          <FaNewspaper className={`h-8 w-8 sm:h-12 sm:w-12 text-[${websiteColorPalette.primary}]`} />
                        </div>
                        <h3 className="text-base sm:text-lg font-medium text-gray-900">No news available</h3>
                        <p className="mt-1 text-gray-500 text-center max-w-md text-sm">
                          When the superadmin sends news to your university, it will appear here.
                          Check back later for updates or click the refresh button above.
                        </p>
                      </div>
                    ) : (
                      news.map((item) => (
                        <motion.div
                          key={item._id}
                          variants={itemVariant}
                          className="bg-white border border-gray-100 rounded-lg p-4 sm:p-5 shadow-sm hover:shadow-md transition-shadow duration-300"
                        >
                          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3">
                            <h3 className="text-base sm:text-lg font-semibold text-gray-900 flex items-center">
                              {item.isGlobal ? (
                                <FaGlobeAmericas className={`text-[${websiteColorPalette.primary}] mr-2 flex-shrink-0 h-4 w-4 sm:h-5 sm:w-5`} />
                              ) : (
                                <FaUniversity className={`text-[${websiteColorPalette.primary}] mr-2 flex-shrink-0 h-4 w-4 sm:h-5 sm:w-5`} />
                              )}
                              {getDisplayText(item.title)}
                              {item.isGlobal && (
                                <span className={`ml-2 px-2 py-0.5 bg-blue-100 text-[${websiteColorPalette.primary}] text-xs rounded-full`}>
                                  Global
                                </span>
                              )}
                            </h3>
                            <div className="flex items-center text-xs text-gray-500">
                              <FaCalendarAlt className="mr-1 h-3 w-3 sm:h-4 sm:w-4" />
                              {formatDate(item.createdAt)}
                            </div>
                          </div>
                          <div className="mt-3 text-gray-700 bg-gray-50 p-3 rounded-md border border-gray-100 text-sm">
                            {getDisplayText(item.content)}
                          </div>
                        </motion.div>
                      ))
                    )}
                  </div>
                </div>
              </motion.div>
            </motion.div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default News;