import React, { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FaFont, FaPen, FaEraser, FaSignature, FaSave, FaTimes, FaCheck } from 'react-icons/fa';
import axios from 'axios';
import { useAuth } from '../context/AuthContext';

// Website color palette
const colorPalette = {
  primary: '#0077B6',
  secondary: '#20B2AA',
  background: '#FFFFFF',
  text: '#333333',
  accent: '#28A745'
};

const SignatureManager = ({ onSignatureSelect, initialSignature = null }) => {
  const { token } = useAuth();
  const [signatureType, setSignatureType] = useState('text');
  const [textSignature, setTextSignature] = useState('');
  const [savedSignature, setSavedSignature] = useState(initialSignature);
  const [isEditing, setIsEditing] = useState(false);
  const [currentSignature, setCurrentSignature] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Canvas refs
  const canvasRef = useRef(null);
  const contextRef = useRef(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [canvasContext, setCanvasContext] = useState(null);

  // Initialize canvas when component mounts - matching Consent.jsx implementation
  useEffect(() => {
    const initializeCanvas = () => {
      if (canvasRef.current) {
        const canvas = canvasRef.current;

        // Set fixed canvas dimensions for consistent experience
        canvas.width = 600;
        canvas.height = 150;

        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        // Set drawing properties
        ctx.lineCap = 'round';
        ctx.lineJoin = 'round';
        ctx.strokeStyle = 'black';
        ctx.lineWidth = 2;

        contextRef.current = ctx;

        // Set white background
        ctx.fillStyle = 'white';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // If we have a saved image signature, draw it on the canvas
        if (signatureType === 'draw' && currentSignature && currentSignature.startsWith('data:image')) {
          const img = new Image();
          img.onload = () => {
            // Clear canvas first
            ctx.fillStyle = 'white';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Draw the image centered and scaled appropriately
            ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
          };
          img.src = currentSignature;
        }
      }
    };

    // Initialize immediately and also after a short delay to ensure DOM is ready
    initializeCanvas();
    const timer = setTimeout(initializeCanvas, 100);

    // Also reinitialize when the window is resized
    const handleResize = () => {
      initializeCanvas();
    };
    window.addEventListener('resize', handleResize);

    return () => {
      clearTimeout(timer);
      window.removeEventListener('resize', handleResize);
    };
  }, [signatureType, currentSignature, isEditing]);

  // Fetch saved signature on mount
  useEffect(() => {
    if (initialSignature) {
      setSavedSignature(initialSignature);
      setCurrentSignature(initialSignature);
    } else {
      fetchSavedSignature();
    }
  }, [initialSignature]);

  const fetchSavedSignature = async () => {
    try {
      setLoading(true);
      const config = { headers: { Authorization: `Bearer ${token}` } };
      const response = await axios.get('http://localhost:5000/api/supervisors/signature', config);

      if (response.data && response.data.signature) {
        setSavedSignature(response.data.signature);
        setCurrentSignature(response.data.signature);

        // Set the signature type based on the saved signature
        if (response.data.signature.startsWith('data:image')) {
          setSignatureType('draw');
        } else {
          setSignatureType('text');
          setTextSignature(response.data.signature);
        }
      }
      setLoading(false);
    } catch (err) {
      console.error('Error fetching signature:', err);
      setError('Failed to load saved signature');
      setLoading(false);
    }
  };

  // Mouse position helper - matching Consent.jsx implementation
  const getMousePos = (canvas, evt) => {
    const rect = canvas.getBoundingClientRect();
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;

    return {
      x: (evt.clientX - rect.left) * scaleX,
      y: (evt.clientY - rect.top) * scaleY
    };
  };

  // Canvas drawing functions - matching Consent.jsx implementation
  const startDrawing = (e) => {
    if (!canvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const pos = getMousePos(canvas, e);

    // Set drawing style
    ctx.lineWidth = 2;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
    ctx.strokeStyle = 'black';

    ctx.beginPath();
    ctx.moveTo(pos.x, pos.y);
    setCanvasContext(ctx);
    setIsDrawing(true);
  };

  const draw = (e) => {
    if (!isDrawing || !canvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const pos = getMousePos(canvas, e);

    ctx.lineTo(pos.x, pos.y);
    ctx.stroke();
  };

  const finishDrawing = () => {
    if (isDrawing && canvasRef.current) {
      const ctx = canvasRef.current.getContext('2d');
      if (ctx) {
        ctx.closePath();
      }
      setIsDrawing(false);

      // Save the canvas content as an image
      const dataUrl = canvasRef.current.toDataURL('image/png');
      setCurrentSignature(dataUrl);
    }
  };

  const clearCanvas = () => {
    if (!canvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear the canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Reset to white background
    ctx.fillStyle = 'white';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Reset drawing state
    setIsDrawing(false);
    setCurrentSignature(null);

    // Reset context properties
    ctx.lineWidth = 2;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
    ctx.strokeStyle = 'black';
    setCanvasContext(ctx);
  };

  const handleTextSignatureChange = (e) => {
    const text = e.target.value;
    setTextSignature(text);
    setCurrentSignature(text);
  };

  const toggleSignatureType = () => {
    setSignatureType(prev => prev === 'text' ? 'draw' : 'text');
    setCurrentSignature(null);
    if (canvasRef.current) {
      clearCanvas();
    }
    setTextSignature('');
  };

  const saveSignature = async () => {
    if (!currentSignature) {
      setError('Please create a signature first');
      return;
    }

    try {
      setLoading(true);
      setError('');
      const config = { headers: { Authorization: `Bearer ${token}` } };

      // Save the signature directly to the supervisor's profile
      await axios.post('http://localhost:5000/api/supervisors/signature', { signature: currentSignature }, config);

      setSavedSignature(currentSignature);
      setSuccess('Signature saved successfully!');

      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(''), 3000);

      setIsEditing(false);
      if (onSignatureSelect) {
        onSignatureSelect(currentSignature);
      }
      setLoading(false);
    } catch (err) {
      console.error('Error saving signature:', err);
      setError('Failed to save signature');
      setLoading(false);
    }
  };

  const useSignature = () => {
    if (onSignatureSelect) {
      onSignatureSelect(savedSignature);
    }
  };

  if (!isEditing && savedSignature) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300"
        style={{ borderColor: '#e5e7eb' }}
      >
        <div className="p-4 border-b" style={{
          background: `linear-gradient(to right, ${colorPalette.primary}10, ${colorPalette.secondary}10)`,
          borderColor: '#e5e7eb'
        }}>
          <div className="flex justify-between items-center">
            <h4 className="text-md font-medium flex items-center" style={{ color: colorPalette.primary }}>
              <FaSignature className="mr-2" /> Your Signature
            </h4>
            <button
              onClick={() => setIsEditing(true)}
              className="px-3 py-1 rounded-lg text-sm flex items-center shadow-sm"
              style={{
                backgroundColor: colorPalette.background,
                color: colorPalette.primary
              }}
              onMouseOver={(e) => (e.currentTarget.style.backgroundColor = `${colorPalette.primary}10`)}
              onMouseOut={(e) => (e.currentTarget.style.backgroundColor = colorPalette.background)}
            >
              <FaPen className="mr-1" /> Edit
            </button>
          </div>
        </div>

        <div className="p-6 bg-white">
          <div className="border border-gray-200 rounded-lg p-6 bg-gray-50 mb-4 flex items-center justify-center">
            {savedSignature.startsWith('data:image') ? (
              <img src={savedSignature} alt="Signature" className="max-h-20" />
            ) : (
              <p className="font-signature text-2xl text-center">{savedSignature}</p>
            )}
          </div>

          <motion.button
            whileHover={{ scale: 1.03 }}
            whileTap={{ scale: 0.97 }}
            onClick={useSignature}
            className="w-full px-4 py-3 text-white rounded-lg flex items-center justify-center shadow-sm font-medium"
            style={{
              background: `linear-gradient(to right, ${colorPalette.primary}, ${colorPalette.secondary})`
            }}
            onMouseOver={(e) => (e.currentTarget.style.filter = 'brightness(1.1)')}
            onMouseOut={(e) => (e.currentTarget.style.filter = 'brightness(1)')}
          >
            <FaSignature className="mr-2" /> Use This Signature
          </motion.button>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300"
      style={{ borderColor: '#e5e7eb' }}
    >
      <div className="p-4 border-b" style={{
        background: `linear-gradient(to right, ${colorPalette.primary}10, ${colorPalette.secondary}10)`,
        borderColor: '#e5e7eb'
      }}>
        <div className="flex justify-between items-center">
          <h4 className="text-md font-medium flex items-center" style={{ color: colorPalette.primary }}>
            <FaSignature className="mr-2" />
            {isEditing ? 'Edit Your Signature' : 'Create a Signature'}
          </h4>
          {isEditing && (
            <button
              onClick={() => setIsEditing(false)}
              className="px-3 py-1 rounded-lg text-sm flex items-center shadow-sm"
              style={{
                backgroundColor: colorPalette.background,
                color: colorPalette.text
              }}
              onMouseOver={(e) => (e.currentTarget.style.backgroundColor = '#f3f4f6')}
              onMouseOut={(e) => (e.currentTarget.style.backgroundColor = colorPalette.background)}
            >
              <FaTimes className="mr-1" /> Cancel
            </button>
          )}
        </div>
      </div>

      <div className="p-6 bg-white">
        {error && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="mb-4 p-3 bg-red-50 text-red-700 rounded-lg text-sm border border-red-100"
          >
            {error}
          </motion.div>
        )}

        {success && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="mb-4 p-3 bg-green-50 text-green-700 rounded-lg text-sm border border-green-100 flex items-center"
          >
            <FaCheck className="mr-2" /> {success}
          </motion.div>
        )}

        <div className="mb-6">
          <div className="flex space-x-4 mb-4">
            <button
              onClick={() => setSignatureType('text')}
              className="flex items-center px-4 py-2 rounded-lg border"
              style={{
                backgroundColor: signatureType === 'text' ? `${colorPalette.primary}15` : '#f9fafb',
                color: signatureType === 'text' ? colorPalette.primary : colorPalette.text,
                borderColor: signatureType === 'text' ? colorPalette.primary : '#e5e7eb'
              }}
            >
              <FaFont className="mr-2" /> Text Signature
            </button>
            <button
              onClick={() => setSignatureType('draw')}
              className="flex items-center px-4 py-2 rounded-lg border"
              style={{
                backgroundColor: signatureType === 'draw' ? `${colorPalette.primary}15` : '#f9fafb',
                color: signatureType === 'draw' ? colorPalette.primary : colorPalette.text,
                borderColor: signatureType === 'draw' ? colorPalette.primary : '#e5e7eb'
              }}
            >
              <FaPen className="mr-2" /> Draw Signature
            </button>
          </div>
        </div>

        {signatureType === 'text' && (
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Type your full name as your signature:
            </label>
            <input
              type="text"
              value={textSignature}
              onChange={handleTextSignatureChange}
              placeholder="Type your full name here"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-signature text-xl"
            />
          </div>
        )}

        {signatureType === 'draw' && (
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Draw your signature below:
            </label>
            <div className="border border-gray-300 rounded-lg overflow-hidden bg-white">
              <canvas
                ref={canvasRef}
                width={600}
                height={150}
                onMouseDown={startDrawing}
                onMouseMove={draw}
                onMouseUp={finishDrawing}
                onMouseLeave={finishDrawing}
                onTouchStart={(e) => {
                  e.preventDefault(); // Prevent scrolling when drawing
                  if (e.touches && e.touches.length > 0) {
                    const touch = e.touches[0];
                    startDrawing({
                      clientX: touch.clientX,
                      clientY: touch.clientY
                    });
                  }
                }}
                onTouchMove={(e) => {
                  e.preventDefault(); // Prevent scrolling when drawing
                  if (e.touches && e.touches.length > 0) {
                    const touch = e.touches[0];
                    draw({
                      clientX: touch.clientX,
                      clientY: touch.clientY
                    });
                  }
                }}
                onTouchEnd={finishDrawing}
                className="w-full cursor-crosshair"
              />
            </div>
            <button
              onClick={clearCanvas}
              className="mt-2 px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded hover:bg-gray-300 flex items-center w-auto"
            >
              <FaEraser className="mr-1" /> Clear Canvas
            </button>
          </div>
        )}

        {currentSignature && (
          <div className="mb-6">
            <h4 className="text-sm font-medium text-gray-700 mb-2">Signature Preview</h4>
            <div className="border border-gray-300 rounded-lg bg-gray-50 p-6 flex items-center justify-center">
              {signatureType === 'text' ? (
                <p className="font-signature text-2xl">{currentSignature}</p>
              ) : (
                <img src={currentSignature} alt="Signature" className="max-h-24" />
              )}
            </div>
          </div>
        )}

        <motion.button
          whileHover={{ scale: 1.03 }}
          whileTap={{ scale: 0.97 }}
          onClick={saveSignature}
          disabled={!currentSignature || loading}
          className="w-full px-4 py-3 rounded-lg flex items-center justify-center shadow-sm font-medium"
          style={{
            background: !currentSignature || loading
              ? '#d1d5db'
              : `linear-gradient(to right, ${colorPalette.primary}, ${colorPalette.secondary})`,
            color: !currentSignature || loading ? '#6b7280' : colorPalette.background,
            cursor: !currentSignature || loading ? 'not-allowed' : 'pointer'
          }}
          onMouseOver={(e) => !(!currentSignature || loading) && (e.currentTarget.style.filter = 'brightness(1.1)')}
          onMouseOut={(e) => !(!currentSignature || loading) && (e.currentTarget.style.filter = 'brightness(1)')}
        >
          {loading ? (
            <>
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Saving...
            </>
          ) : (
            <>
              <FaSave className="mr-2" />
              Save Signature
            </>
          )}
        </motion.button>
      </div>
    </motion.div>
  );
};

export default SignatureManager;
