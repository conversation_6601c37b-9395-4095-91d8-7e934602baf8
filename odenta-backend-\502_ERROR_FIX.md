# 502 Error Fix Guide

## 🚨 Error: 502 Bad Gateway

The 502 error means Railway can't reach your application. Here's the complete fix:

## ✅ Comprehensive Solution

### 1. Created New Start Script

**`start.js`** - Robust server startup with:
- Better error handling
- Proper logging
- Graceful shutdown
- Uncaught exception handling
- Railway-specific CORS configuration

### 2. Updated Package.json

**Changed start script:**
```json
{
  "scripts": {
    "start": "node start.js"
  }
}
```

### 3. Railway-Specific CORS

**Added your Railway domain to allowed origins:**
```javascript
const allowedOrigins = [
  'http://localhost:3000',
  'https://odenta.vercel.app',
  'https://Odenta.vercel.app',
  'https://odenta.vercel.app/',
  'https://Odenta.vercel.app/',
  'https://odenta-backend-production-1b94.up.railway.app'  // Your Railway domain
];
```

### 4. Enhanced Error Handling

**Added comprehensive error handling:**
- Uncaught exceptions
- Unhandled rejections
- Graceful shutdown
- Better logging

## 🔧 Root Cause Analysis

The 502 error typically occurs due to:

1. **Application crashes on startup**
2. **Server not binding to correct interface**
3. **Missing environment variables**
4. **Dependency issues**
5. **Firebase connection failures**

## 🎯 Fixes Applied

### 1. Robust Startup Script
- **Better error handling** - Catches and logs all errors
- **Proper binding** - `0.0.0.0` for all interfaces
- **Enhanced logging** - Clear startup messages
- **Graceful shutdown** - Proper cleanup

### 2. Environment Variable Validation
- **Port binding** - Uses `config.PORT` correctly
- **Environment detection** - Proper production mode
- **Firebase connection** - Try-catch for connection issues

### 3. Railway-Specific Configuration
- **CORS updates** - Added your Railway domain
- **Health check** - Enhanced health endpoint
- **Error responses** - Better error handling

## 🚀 Deployment Steps

### Step 1: Commit and Push
```bash
cd odenta-backend-
git add .
git commit -m "Fix 502 error: robust startup script and Railway configuration"
git push origin main
```

### Step 2: Verify Environment Variables
In Railway dashboard, ensure these are set:

```env
# Server Configuration
PORT=8080
NODE_ENV=production

# Database Configuration
MONGO_URI=mongodb+srv://username:<EMAIL>/dentlyzer

# JWT Configuration
JWT_SECRET=your_production_jwt_secret_here
JWT_REFRESH_SECRET=your_production_refresh_secret_here
JWT_ACCESS_EXPIRATION=15m
JWT_REFRESH_EXPIRATION=7d

# Frontend URL
FRONTEND_URL=https://your-vercel-app.vercel.app

# File Upload Configuration
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=50mb
```

### Step 3: Monitor Deployment
1. Go to Railway Dashboard
2. Check deployment logs
3. Look for any error messages
4. Verify the application starts successfully

### Step 4: Test Endpoints
After deployment, test:

1. **Health Check:**
   ```
   https://odenta-backend-production-1b94.up.railway.app/api/health
   ```
   Should return:
   ```json
   {
     "status": "OK",
     "timestamp": "2025-07-20T...",
     "environment": "production",
     "port": 8080
   }
   ```

2. **Root Endpoint:**
   ```
   https://odenta-backend-production-1b94.up.railway.app/
   ```
   Should return: `Welcome to the ODenta API!`

## 🔍 Troubleshooting

### If Still Getting 502:

1. **Check Railway Logs**
   - Go to Railway Dashboard
   - Click on your project
   - Go to Deployments tab
   - Click on latest deployment
   - Check logs for error messages

2. **Common Issues:**
   - **Missing environment variables** - Set all required variables
   - **Firebase connection failed** - Check Firebase credentials
   - **Port conflicts** - Ensure PORT is set correctly
   - **Dependency issues** - Check for missing dependencies

3. **Debug Steps:**
   - Check if application starts in logs
   - Verify all environment variables are set
   - Test health endpoint directly
   - Check Firebase connection

## 📋 Success Indicators

After successful deployment, you should see in logs:

```
🚀 Starting ODenta Backend Server...
📡 Port: 8080
🌍 Environment: production
🔗 Frontend URL: https://your-vercel-app.vercel.app
✅ Firebase connected successfully
✅ Server running on port 8080
🌐 Server URL: http://0.0.0.0:8080
🔗 Health check: http://0.0.0.0:8080/api/health
```

And the health endpoint should respond successfully.

## 🎯 Expected Results

- ✅ No 502 errors
- ✅ Health endpoint responds
- ✅ Application starts successfully
- ✅ All environment variables loaded
- ✅ Firebase connection established
- ✅ Server bound to correct interface

The new `start.js` script provides robust error handling and Railway-specific configuration that should resolve the 502 error completely. 