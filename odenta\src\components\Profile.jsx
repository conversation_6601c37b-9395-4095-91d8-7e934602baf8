import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { useAuth } from '../context/AuthContext';
import Navbar from '../student/Navbar';
import Sidebar from '../student/Sidebar';
import Loader from '../components/Loader';
import { motion } from 'framer-motion';
import { FaUser, FaUniversity, FaEnvelope, FaIdCard } from 'react-icons/fa';

const Profile = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [userData, setUserData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const navigate = useNavigate();
  const { user, token } = useAuth();

  useEffect(() => {
    const fetchUserData = async () => {
      if (!user || !token) {
        setError('Please log in to view your profile.');
        setLoading(false);
        return;
      }

      try {
        const config = { headers: { Authorization: `Bearer ${token}` } };
        const response = await axios.get('http://localhost:5000/api/auth/profile', config);
        setUserData(response.data);
      } catch (err) {
        console.error('Fetch user data error:', err.response?.data || err.message);
        const errorMessage = err.response?.status === 401
          ? 'Unauthorized. Please log in again.'
          : err.response?.data?.message || 'Failed to load profile data';
        setError(errorMessage);
        if (err.response?.status === 401) {
          navigate('/login');
        }
      } finally {
        setLoading(false);
      }
    };
    fetchUserData();
  }, [user, token, navigate]);

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 }
  };

  if (loading) {
    return <Loader />;
  }

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />

      <div className="flex-1 flex flex-col overflow-hidden">
        <Navbar toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />

        <main className="flex-1 overflow-y-auto p-6 bg-gradient-to-br from-[rgba(0,119,182,0.05)] to-white">
          <div className="max-w-4xl mx-auto">
            {error && (
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-lg shadow-sm"
              >
                <div className="flex items-center">
                  <svg className="w-5 h-5 text-red-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                  <p className="text-red-700 font-medium">{error}</p>
                </div>
              </motion.div>
            )}

            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              <div className="mb-8">
                <h1 className="text-3xl md:text-4xl font-bold text-[#0077B6] mb-1">
                  Your Profile
                </h1>
                <p className="text-[#333333]">View your account details</p>
              </div>

              {userData && (
                <motion.div
                  variants={container}
                  initial="hidden"
                  animate="show"
                  className="space-y-6"
                >
                  <motion.div
                    variants={item}
                    className="bg-white p-6 rounded-xl shadow-sm border border-[rgba(0,119,182,0.1)]"
                  >
                    <h3 className="text-lg font-semibold text-[#0077B6] mb-4 flex items-center">
                      <FaUser className="h-5 w-5 mr-2 text-[#0077B6]" />
                      Personal Information
                    </h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div>
                        <h4 className="text-sm font-medium text-gray-500">Name</h4>
                        <p className="text-sm text-[#333333] mt-1">{userData.name}</p>
                      </div>
                      <div>
                        <h4 className="text-sm font-medium text-gray-500">Email</h4>
                        <p className="text-sm text-[#333333] mt-1">{userData.email}</p>
                      </div>
                      <div>
                        <h4 className="text-sm font-medium text-gray-500">Role</h4>
                        <p className="text-sm text-[#333333] mt-1 capitalize">{userData.role}</p>
                      </div>
                      <div>
                        <h4 className="text-sm font-medium text-gray-500">University</h4>
                        <p className="text-sm text-[#333333] mt-1">{userData.university || 'N/A'}</p>
                      </div>
                      {userData.role === 'student' && (
                        <div>
                          <h4 className="text-sm font-medium text-gray-500">Student ID</h4>
                          <p className="text-sm text-[#333333] mt-1">{userData.studentId || 'N/A'}</p>
                        </div>
                      )}
                    </div>
                  </motion.div>
                </motion.div>
              )}
            </motion.div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default Profile;